import gspread
from oauth2client.service_account import ServiceAccountCredentials
import requests
import os
import random
import string
import time
from urllib.parse import urlparse

# ===== CẤU HÌNH =====
MAX_ROWS = 3  # Số dòng tối đa muốn xử lý (None = tất cả dòng)
DOWNLOAD_FOLDER = r"C:\Users\<USER>\Downloads\EC-Product-Images"
DELAY_BETWEEN_DOWNLOADS = 0.5  # Giây delay giữa các lần download

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("Medicine")
    print("✅ Đã kết nối Google Sheets thành công!")
    return sheet

def check_product_images_exist(product_code, folder_path):
    """Kiểm tra xem đã có ảnh nào của sản phẩm này chưa"""
    if not os.path.exists(folder_path):
        return False

    # Lấy tất cả file trong folder
    files = os.listdir(folder_path)

    # Kiểm tra có file nào chứa mã sản phẩm không
    for file in files:
        if product_code in file:
            return True

    return False

def generate_product_filename(product_code, image_index, extension):
    """Tạo tên file theo format: P00478_1.jpg"""
    return f"{product_code}_{image_index}{extension}"

def get_file_extension(url):
    """Lấy extension từ URL"""
    parsed_url = urlparse(url)
    path = parsed_url.path
    if path.endswith('.jpg') or path.endswith('.jpeg'):
        return '.jpg'
    elif path.endswith('.png'):
        return '.png'
    elif path.endswith('.webp'):
        return '.webp'
    else:
        return '.jpg'  # Default

def download_image(url, folder_path, filename):
    """Download ảnh từ URL"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://www.pharmacity.vn/',
            'Accept': 'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'image',
            'Sec-Fetch-Mode': 'no-cors',
            'Sec-Fetch-Site': 'cross-site'
        }

        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()

        file_path = os.path.join(folder_path, filename)

        with open(file_path, 'wb') as f:
            f.write(response.content)

        return True, file_path

    except Exception as e:
        return False, str(e)

def create_download_folder():
    """Tạo folder download nếu chưa có"""
    if not os.path.exists(DOWNLOAD_FOLDER):
        os.makedirs(DOWNLOAD_FOLDER)
        print(f"📁 Đã tạo folder: {DOWNLOAD_FOLDER}")
    else:
        print(f"📁 Folder đã tồn tại: {DOWNLOAD_FOLDER}")

def main():
    """Hàm main"""
    print("🖼️ BẮT ĐẦU DOWNLOAD ẢNH SẢN PHẨM")
    print("="*60)
    print(f"📊 Cấu hình: Tối đa {MAX_ROWS if MAX_ROWS else 'TẤT CẢ'} dòng")
    print(f"📁 Thư mục lưu: {DOWNLOAD_FOLDER}")
    print("="*60)

    # Tạo folder download
    create_download_folder()

    # Kết nối Google Sheets
    sheet = connect_to_google_sheets()

    # Lấy dữ liệu từ cột C (Mã sản phẩm) và cột R (Ảnh sản phẩm)
    print("\n📋 Đang lấy dữ liệu từ cột C (Mã sản phẩm) và cột R (Ảnh sản phẩm)...")

    try:
        # Lấy tất cả giá trị từ cột C và R
        column_c_values = sheet.col_values(3)   # Cột C = cột 3 (Mã sản phẩm)
        column_r_values = sheet.col_values(18)  # Cột R = cột 18 (Ảnh sản phẩm)

        print(f"📊 Tìm thấy {len(column_c_values)} ô trong cột C")
        print(f"📊 Tìm thấy {len(column_r_values)} ô trong cột R")

        # Bỏ qua header (hàng đầu tiên) và đảm bảo cả 2 cột có cùng độ dài
        max_rows = min(len(column_c_values), len(column_r_values))
        product_codes = column_c_values[1:max_rows] if max_rows > 1 else []
        image_data = column_r_values[1:max_rows] if max_rows > 1 else []

        print(f"📊 Có {len(product_codes)} dòng dữ liệu để xử lý")

        total_images_downloaded = 0
        total_images_failed = 0
        total_products_skipped = 0
        total_products_processed = 0
        rows_processed = 0

        for row_index, (product_code, cell_data) in enumerate(zip(product_codes, image_data), start=2):
            if MAX_ROWS and rows_processed >= MAX_ROWS:
                print(f"\n🛑 Đã xử lý {MAX_ROWS} dòng, dừng download")
                break

            # Đếm dòng đã xử lý (kể cả skip)
            rows_processed += 1

            # Kiểm tra mã sản phẩm có hợp lệ không
            if not product_code or product_code.strip() == "" or product_code == "Không có":
                print(f"\n⚠️ Dòng {rows_processed}/{MAX_ROWS if MAX_ROWS else '∞'} - Hàng {row_index}: Không có mã sản phẩm, bỏ qua")
                continue

            # Kiểm tra dữ liệu ảnh có hợp lệ không
            if not cell_data or cell_data.strip() == "" or cell_data == "Không có":
                print(f"\n⚠️ Dòng {rows_processed}/{MAX_ROWS if MAX_ROWS else '∞'} - Hàng {row_index}: Không có dữ liệu ảnh, bỏ qua")
                continue

            product_code = product_code.strip()
            print(f"\n📄 Dòng {rows_processed}/{MAX_ROWS if MAX_ROWS else '∞'} - Hàng {row_index} - Mã sản phẩm: {product_code}")

            # Kiểm tra xem đã có ảnh của sản phẩm này chưa
            if check_product_images_exist(product_code, DOWNLOAD_FOLDER):
                print(f"  ⏭️ Đã có ảnh của sản phẩm {product_code}, bỏ qua")
                total_products_skipped += 1
                continue

            # Tách các URL trong ô (mỗi dòng là 1 URL)
            image_urls = [url.strip() for url in cell_data.split('\n') if url.strip()]

            print(f"  🔗 Tìm thấy {len(image_urls)} URL ảnh")
            total_products_processed += 1

            for url_index, image_url in enumerate(image_urls, start=1):

                if not image_url.startswith('http'):
                    print(f"    ⚠️ URL không hợp lệ: {image_url[:50]}...")
                    continue

                # Tạo tên file theo format: P00478_1.jpg
                extension = get_file_extension(image_url)
                filename = generate_product_filename(product_code, url_index, extension)

                print(f"    📥 Download {url_index}/{len(image_urls)}: {filename}")
                print(f"       URL: {image_url[:80]}...")

                # Download ảnh
                success, result = download_image(image_url, DOWNLOAD_FOLDER, filename)

                if success:
                    total_images_downloaded += 1
                    print(f"    ✅ Thành công: {result}")
                else:
                    total_images_failed += 1
                    print(f"    ❌ Thất bại: {result}")

                # Delay giữa các lần download
                time.sleep(DELAY_BETWEEN_DOWNLOADS)

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT DOWNLOAD ẢNH!")
        print("="*60)
        print(f"📋 Dòng đã xử lý: {rows_processed}")
        print(f"📦 Sản phẩm đã xử lý: {total_products_processed}")
        print(f"⏭️ Sản phẩm đã bỏ qua (có ảnh): {total_products_skipped}")
        print(f"✅ Ảnh download thành công: {total_images_downloaded}")
        print(f"❌ Ảnh download thất bại: {total_images_failed}")
        print(f"📁 Thư mục lưu: {DOWNLOAD_FOLDER}")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi khi xử lý dữ liệu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
