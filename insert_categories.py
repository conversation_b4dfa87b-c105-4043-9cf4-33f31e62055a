import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import time
import sys
import os
import requests
import uuid
from urllib.parse import urlparse

# ===== CẤU HÌNH =====
# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "Medicine"  # Sheet Medicine như file insert_adminer
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
MAX_ROWS = 7000  # 🎯 KIỂM SOÁT SỐ DÒNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
BATCH_SIZE = 10  # Số lượng records insert cùng lúc (không áp dụng cho categories workflow)
START_ROW = 2000  # Bắt đầu từ dòng nào (2 = bỏ qua header)
CATEGORY_COLUMN = 6  # Cột G (index 6) chứa category hierarchy

# Cấu hình download ảnh cho logo_path và banner_path
IMAGE_DOWNLOAD_PATH = r"C:\Working\Medical-EC\MEDICAL_EC_SYSTEM\storage\app\public\categories"
IMAGE_URL_PREFIX = "categories/"  # Prefix để lưu vào DB

# ===== CẤU HÌNH CÁC BẢNG =====
# Chọn bảng muốn test: 'categories' (sẽ insert cả 3 bảng cùng lúc)
ACTIVE_TABLE = "categories"  # Workflow đặc biệt cho 3 bảng

# Cấu hình cho từng bảng
TABLE_CONFIGS = {
    "categories": {
        "table_name": "categories",
        "columns": ["position", "logo_path", "status", "display_mode", "_lft", "_rgt", "parent_id", "additional", "banner_path", "created_at", "updated_at"],
        "mapping": {
            'position': 0,           # Cột A - Position
            'logo_path': 1,          # Cột B - Logo URL
            'status': 2,             # Cột C - Status (0/1)
            'display_mode': 3,       # Cột D - Display Mode
            'parent_id': 4,          # Cột E - Parent ID
            'banner_path': 5,        # Cột F - Banner URL
        },
        "processor": "process_categories_data"
    },
    "category_translations": {
        "table_name": "category_translations",
        "columns": ["category_id", "name", "slug", "url_path", "description", "meta_title", "meta_description", "meta_keywords", "locale_id", "locale"],
        "mapping": {
            'name': 6,               # Cột G - Category Name
            'slug': 7,               # Cột H - Slug
            'description': 8,        # Cột I - Description
            'meta_title': 9,         # Cột J - Meta Title
            'meta_description': 10,  # Cột K - Meta Description
            'meta_keywords': 11,     # Cột L - Meta Keywords
        },
        "processor": "process_category_translations_data"
    },
    "category_filterable_attributes": {
        "table_name": "category_filterable_attributes",
        "columns": ["category_id", "attribute_id"],
        "mapping": {
            'attribute_id': 12,      # Cột M - Attribute ID
        },
        "processor": "process_category_filterable_attributes_data"
    }
}

# Lấy config của bảng hiện tại
CURRENT_CONFIG = TABLE_CONFIGS[ACTIVE_TABLE]
TABLE_NAME = CURRENT_CONFIG["table_name"]

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print("✅ Đã kết nối Google Sheets thành công!")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        print(f"💡 Vui lòng tạo sheet '{SHEET_NAME}' trong Google Sheets trước")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    print("🔗 Đang kết nối MySQL Docker (Adminer)...")
    print(f"🖥️ Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 User: {DB_CONFIG['user']}")
    print(f"🗄️ Database: {DB_CONFIG['database']}")

    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL Docker thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL Docker: {e}")
        print("💡 Thử các cách sau:")
        print("   - Đảm bảo Docker đang chạy")
        print("   - Kiểm tra port 3307 có đúng không")
        print("   - Thử thay 'localhost' bằng 'mysql' hoặc '127.0.0.1'")
        print("   - Kiểm tra Adminer: http://localhost:8080")
        sys.exit(1)

def check_tables_exist(connection):
    """Kiểm tra 3 bảng categories trong Docker MySQL"""
    tables_to_check = ["categories", "category_translations", "category_filterable_attributes"]

    for table_name in tables_to_check:
        print(f"🔧 Kiểm tra table {table_name} trong Docker MySQL...")

        # Kiểm tra xem table đã tồn tại chưa
        check_table_sql = f"""
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = '{DB_CONFIG['database']}'
        AND table_name = '{table_name}'
        """

        try:
            with connection.cursor() as cursor:
                cursor.execute(check_table_sql)
                result = cursor.fetchone()

                if result['count'] > 0:
                    print(f"✅ Table {table_name} đã tồn tại trong Docker!")

                    # Kiểm tra cấu trúc table
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"📋 Cấu trúc table {table_name}:")
                    for col in columns:
                        print(f"  - {col['Field']}: {col['Type']}")
                    print()
                else:
                    print(f"❌ Table {table_name} chưa tồn tại trong Docker!")
                    print("💡 Vui lòng tạo table trong Adminer trước: http://localhost:8080")
                    sys.exit(1)

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra table {table_name}: {e}")
            sys.exit(1)

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")

    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()

        # Tính toán số dòng thực tế có data (bỏ qua header)
        total_rows_with_header = len(all_data)
        total_data_rows = total_rows_with_header - (START_ROW - 1)

        print(f"📊 Sheet có tổng cộng {total_rows_with_header} dòng (bao gồm header)")
        print(f"📊 Số dòng data thực tế: {total_data_rows} dòng")

        # Bỏ qua header
        data_rows = all_data[START_ROW-1:]

        # Giới hạn số lượng dòng nếu cần
        if MAX_ROWS:
            if MAX_ROWS > total_data_rows:
                print(f"⚠️  MAX_ROWS ({MAX_ROWS}) lớn hơn số dòng data thực tế ({total_data_rows})")
                print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data có sẵn")
            else:
                print(f"📋 Giới hạn xử lý {MAX_ROWS} dòng đầu tiên từ {total_data_rows} dòng data")
                data_rows = data_rows[:MAX_ROWS]
        else:
            print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data")

        actual_rows_to_process = len(data_rows)
        print(f"✅ Đã lấy {actual_rows_to_process} dòng dữ liệu từ Google Sheets để xử lý")

        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        sys.exit(1)

# ===== UTILITY FUNCTIONS =====

def ensure_directory_exists(directory_path):
    """Tạo thư mục nếu chưa tồn tại"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"📁 Đã tạo thư mục: {directory_path}")

def download_image(image_url, save_directory):
    """Download ảnh từ URL và trả về tên file"""
    try:
        if not image_url or image_url.strip() == "":
            return None

        # Tạo tên file unique
        file_extension = ".png"  # Mặc định .png
        try:
            parsed_url = urlparse(image_url)
            original_extension = os.path.splitext(parsed_url.path)[1]
            if original_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                file_extension = original_extension
        except:
            pass

        filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = os.path.join(save_directory, filename)

        # Download ảnh
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()

        with open(file_path, 'wb') as f:
            f.write(response.content)

        print(f"  📥 Downloaded: {filename}")
        return filename

    except Exception as e:
        print(f"  ❌ Lỗi download ảnh {image_url}: {e}")
        return None

# ===== DATA PROCESSORS CHO TỪNG BẢNG =====

def create_slug(text):
    """Tạo slug từ text tiếng Việt"""
    import unicodedata
    import re

    # Chuyển về lowercase
    text = text.lower()

    # Loại bỏ dấu tiếng Việt
    text = unicodedata.normalize('NFD', text)
    text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')

    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    text = re.sub(r'[^a-z0-9]+', '-', text)

    # Loại bỏ dấu gạch ngang ở đầu và cuối
    text = text.strip('-')

    return text

def check_category_exists(connection, category_name):
    """Kiểm tra category đã tồn tại chưa và trả về ID nếu có"""
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT c.id
            FROM category_translations ct
            JOIN categories c ON ct.category_id = c.id
            WHERE ct.name = %s
            """
            cursor.execute(sql, (category_name,))
            result = cursor.fetchone()
            return result['id'] if result else None
    except Exception as e:
        print(f"  ❌ Lỗi khi check category '{category_name}': {e}")
        return None

def insert_single_category(connection, category_name, parent_id=None):
    """Insert 1 category vào 3 bảng và trả về category_id"""
    try:
        with connection.cursor() as cursor:
            # Bước 1: Insert vào categories
            cat_sql = """
            INSERT INTO categories (position, logo_path, status, display_mode, _lft, _rgt, parent_id, additional, banner_path, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
            """
            cat_values = (1, None, 1, "products_and_description", 15, 16, parent_id, None, None)
            cursor.execute(cat_sql, cat_values)

            # Lấy ID vừa insert
            category_id = cursor.lastrowid
            print(f"    ✅ Inserted categories: ID={category_id}, name='{category_name}', parent_id={parent_id}")

            # Bước 2: Insert vào category_translations
            slug = create_slug(category_name)
            trans_sql = """
            INSERT INTO category_translations (category_id, name, slug, url_path, description, meta_title, meta_description, meta_keywords, locale_id, locale)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            trans_values = (category_id, category_name, slug, "", "", "", "", "", 1, "en")
            cursor.execute(trans_sql, trans_values)

            print(f"    ✅ Inserted category_translations: category_id={category_id}, slug='{slug}'")

            # Bước 3: Insert vào category_filterable_attributes
            filter_sql = """
            INSERT INTO category_filterable_attributes (category_id, attribute_id)
            VALUES (%s, %s)
            """
            filter_values = (category_id, 43)
            cursor.execute(filter_sql, filter_values)

            print(f"    ✅ Inserted category_filterable_attributes: category_id={category_id}, attribute_id=43")

            return category_id

    except Exception as e:
        print(f"    ❌ Lỗi khi insert category '{category_name}': {e}")
        raise e

def process_category_hierarchy(connection, category_string):
    """Xử lý chuỗi category hierarchy và insert các category cần thiết"""
    if not category_string or category_string.strip() == "":
        return []

    # Tách chuỗi theo dấu ";"
    categories = [cat.strip() for cat in category_string.split(';') if cat.strip()]

    if not categories:
        return []

    print(f"  📋 Phân tích hierarchy: {' → '.join(categories)}")

    processed_categories = []
    parent_id = None

    # Xử lý từng category theo thứ tự từ trái sang phải
    for i, category_name in enumerate(categories):
        print(f"    🔍 Kiểm tra category '{category_name}'...")

        # Kiểm tra category đã tồn tại chưa
        existing_id = check_category_exists(connection, category_name)

        if existing_id:
            print(f"    ⏭️ Category '{category_name}' đã tồn tại với ID={existing_id}")
            category_id = existing_id
        else:
            print(f"    🆕 Tạo mới category '{category_name}' với parent_id={parent_id}")
            category_id = insert_single_category(connection, category_name, parent_id)

        processed_categories.append({
            'id': category_id,
            'name': category_name,
            'parent_id': parent_id,
            'level': i
        })

        # Category hiện tại sẽ là parent của category tiếp theo
        parent_id = category_id

    return processed_categories

def insert_categories_workflow(connection, data_rows):
    """Insert workflow: Xử lý category hierarchy từ cột G"""
    print("🚀 BẮT ĐẦU WORKFLOW INSERT CATEGORIES HIERARCHY (DOCKER)")
    print("📋 Workflow: Đọc cột G → Phân tích hierarchy → Insert 3 bảng")

    total_success = 0
    total_failed = 0
    total_skipped = 0
    processed_hierarchies = set()  # Để tránh duplicate hierarchies

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")

                    # Lấy dữ liệu từ cột G (CATEGORY_COLUMN)
                    if len(row) <= CATEGORY_COLUMN:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Không có dữ liệu cột G")
                        total_skipped += 1
                        continue

                    category_string = row[CATEGORY_COLUMN].strip()

                    if not category_string:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Cột G rỗng")
                        total_skipped += 1
                        continue

                    print(f"  📝 Dữ liệu cột G: '{category_string}'")

                    # Kiểm tra duplicate hierarchy
                    if category_string in processed_hierarchies:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Hierarchy '{category_string}' đã xử lý")
                        total_skipped += 1
                        continue

                    processed_hierarchies.add(category_string)

                    # Xử lý category hierarchy
                    categories = process_category_hierarchy(connection, category_string)

                    if categories:
                        print(f"  ✅ Đã xử lý {len(categories)} categories trong hierarchy")
                        for cat in categories:
                            print(f"    📂 Level {cat['level']}: {cat['name']} (ID={cat['id']})")

                        # Commit transaction cho từng hierarchy
                        connection.commit()
                        total_success += 1
                    else:
                        print(f"  ❌ Không thể xử lý hierarchy")
                        total_failed += 1
                        connection.rollback()

                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    connection.rollback()
                    continue

        print(f"\n📊 KẾT QUẢ WORKFLOW CATEGORIES HIERARCHY (DOCKER):")
        print(f"  ✅ Thành công: {total_success} hierarchies")
        print(f"  ⏭️ Bỏ qua: {total_skipped} hierarchies")
        print(f"  ❌ Thất bại: {total_failed} hierarchies")

    except Exception as e:
        print(f"❌ Lỗi workflow: {e}")
        connection.rollback()



def check_existing_data(connection):
    """Kiểm tra dữ liệu hiện có trong các table"""
    tables_to_check = ["categories", "category_translations", "category_filterable_attributes"]

    for table_name in tables_to_check:
        print(f"🔍 Kiểm tra dữ liệu hiện có trong table {table_name} (Docker)...")

        try:
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                result = cursor.fetchone()
                count = result['count']
                print(f"📊 Hiện có {count} records trong table {table_name}")

                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 1")
                    sample = cursor.fetchone()
                    print("📋 Mẫu dữ liệu:")
                    for key, value in sample.items():
                        print(f"  - {key}: {value}")
                print()
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra dữ liệu {table_name}: {e}")

def main():
    """Hàm main cho Categories workflow"""
    print("🚀 BẮT ĐẦU INSERT CATEGORIES VÀO DOCKER MYSQL (ADMINER)")
    print("="*60)
    print(f"📊 Cấu hình: Insert tối đa {MAX_ROWS if MAX_ROWS else 'TẤT CẢ'} dòng")
    print(f"📦 Batch size: {BATCH_SIZE}")
    print(f"🐳 Docker Database: {DB_CONFIG['database']}")
    print(f"🖥️ Docker Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"📋 Active Workflow: 3 Tables Categories")
    print(f"📋 Tables: categories, category_translations, category_filterable_attributes")
    print(f"🌐 Adminer URL: http://localhost:8080")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối Docker MySQL
        connection = connect_to_mysql()

        # Kiểm tra 3 tables trong Docker
        check_tables_exist(connection)

        # Kiểm tra dữ liệu hiện có
        check_existing_data(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Insert dữ liệu vào Docker MySQL (3 tables với hierarchy)
        insert_categories_workflow(connection, data_rows)

        # Kiểm tra lại sau khi insert
        print("\n🔍 Kiểm tra lại sau khi insert:")
        check_existing_data(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT INSERT CATEGORIES VÀO DOCKER!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
