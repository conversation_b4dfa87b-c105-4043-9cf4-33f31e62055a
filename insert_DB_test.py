import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import time
import sys
import os
import requests
import uuid
from urllib.parse import urlparse

# ===== CẤU HÌNH =====
# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "Brands"  # Đổi từ "Medicine" sang "Brands"
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config - Thử nhiều cấu hình khác nhau
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',        # Thử user root (Laragon default)
    'password': '',        # Thử password rỗng (Laragon default)
    'database': 'bagisto',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
MAX_ROWS = None  # 🎯 KIỂM SOÁT SỐ DÒNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
BATCH_SIZE = 10  # Số lượng records insert cùng lúc (không áp dụng cho brands workflow)
START_ROW = 2  # Bắt đầu từ dòng nào (2 = bỏ qua header)

# Cấu hình download ảnh
IMAGE_DOWNLOAD_PATH = r"C:\Working\Medical-EC\MEDICAL_EC_SYSTEM\storage\app\public\brands"
IMAGE_URL_PREFIX = "brands/"  # Prefix để lưu vào DB

# ===== CẤU HÌNH CÁC BẢNG =====
# Chọn bảng muốn test: 'brands' hoặc 'attribute_options'
ACTIVE_TABLE = "brands"  # Thay đổi để test bảng khác

# Cấu hình cho từng bảng
TABLE_CONFIGS = {
    "brands": {
        "table_name": "brands",
        "columns": ["attribute_option_id", "brand_name", "brand_image", "brand_description", "outstanding_product_id"],
        "mapping": {
            'brand_image': 0,        # Cột A - Image URL
            'brand_name': 1,         # Cột B - Brand Name
            'brand_description': 2,  # Cột C - Description
        },
        "processor": "process_brands_data"
    },
    "attribute_options": {
        "table_name": "attribute_options",
        "columns": ["attribute_id", "admin_name", "sort_order", "swatch_value", "is_featured", "brand_image", "brand_description"],
        "mapping": {
            'admin_name': 1,         # Cột B - Brand Name từ sheet Brands
        },
        "processor": "process_attribute_options_data"
    }
}

# Lấy config của bảng hiện tại
CURRENT_CONFIG = TABLE_CONFIGS[ACTIVE_TABLE]
TABLE_NAME = CURRENT_CONFIG["table_name"]

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print("✅ Đã kết nối Google Sheets thành công!")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL"""
    print("🔗 Đang kết nối MySQL...")
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL: {e}")
        sys.exit(1)

def create_table_if_not_exists(connection):
    """Tạo table brands nếu chưa tồn tại (theo cấu trúc bạn cung cấp)"""
    print(f"🔧 Kiểm tra table {TABLE_NAME}...")

    # Kiểm tra xem table đã tồn tại chưa
    check_table_sql = f"""
    SELECT COUNT(*) as count
    FROM information_schema.tables
    WHERE table_schema = '{DB_CONFIG['database']}'
    AND table_name = '{TABLE_NAME}'
    """

    try:
        with connection.cursor() as cursor:
            cursor.execute(check_table_sql)
            result = cursor.fetchone()

            if result['count'] > 0:
                print(f"✅ Table {TABLE_NAME} đã tồn tại!")

                # Kiểm tra cấu trúc table
                cursor.execute(f"DESCRIBE {TABLE_NAME}")
                columns = cursor.fetchall()
                print(f"📋 Cấu trúc table {TABLE_NAME}:")
                for col in columns:
                    print(f"  - {col['Field']}: {col['Type']}")
            else:
                print(f"❌ Table {TABLE_NAME} chưa tồn tại!")
                print("💡 Vui lòng tạo table brands trước hoặc kiểm tra tên database/table")
                sys.exit(1)

    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra table: {e}")
        sys.exit(1)

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")

    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()

        # Tính toán số dòng thực tế có data (bỏ qua header)
        total_rows_with_header = len(all_data)
        total_data_rows = total_rows_with_header - (START_ROW - 1)

        print(f"📊 Sheet có tổng cộng {total_rows_with_header} dòng (bao gồm header)")
        print(f"📊 Số dòng data thực tế: {total_data_rows} dòng")

        # Bỏ qua header
        data_rows = all_data[START_ROW-1:]

        # Giới hạn số lượng dòng nếu cần
        if MAX_ROWS:
            if MAX_ROWS > total_data_rows:
                print(f"⚠️  MAX_ROWS ({MAX_ROWS}) lớn hơn số dòng data thực tế ({total_data_rows})")
                print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data có sẵn")
                # Không cần slice vì đã ít hơn MAX_ROWS
            else:
                print(f"📋 Giới hạn xử lý {MAX_ROWS} dòng đầu tiên từ {total_data_rows} dòng data")
                data_rows = data_rows[:MAX_ROWS]
        else:
            print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data")

        actual_rows_to_process = len(data_rows)
        print(f"✅ Đã lấy {actual_rows_to_process} dòng dữ liệu từ Google Sheets để xử lý")

        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        sys.exit(1)

# ===== UTILITY FUNCTIONS =====

def ensure_directory_exists(directory_path):
    """Tạo thư mục nếu chưa tồn tại"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path)
        print(f"📁 Đã tạo thư mục: {directory_path}")

def download_image(image_url, save_directory):
    """Download ảnh từ URL và trả về tên file"""
    try:
        if not image_url or image_url.strip() == "":
            return None

        # Tạo tên file unique
        file_extension = ".png"  # Mặc định .png
        try:
            parsed_url = urlparse(image_url)
            original_extension = os.path.splitext(parsed_url.path)[1]
            if original_extension in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                file_extension = original_extension
        except:
            pass

        filename = f"{uuid.uuid4().hex}{file_extension}"
        file_path = os.path.join(save_directory, filename)

        # Download ảnh
        response = requests.get(image_url, timeout=30)
        response.raise_for_status()

        with open(file_path, 'wb') as f:
            f.write(response.content)

        print(f"  📥 Downloaded: {filename}")
        return filename

    except Exception as e:
        print(f"  ❌ Lỗi download ảnh {image_url}: {e}")
        return None

# ===== DATA PROCESSORS CHO TỪNG BẢNG =====

def process_attribute_options_data(row):
    """Xử lý dữ liệu cho bảng attribute_options theo logic mới"""
    # Lấy dữ liệu từ sheet Brands
    admin_name = row[1] if len(row) > 1 else ""   # Cột B - Brand Name

    # Kiểm tra dữ liệu hợp lệ
    if not admin_name or admin_name.strip() == "" or admin_name == "Không có":
        return None

    # Làm sạch dữ liệu
    admin_name = admin_name.strip()

    return {
        'attribute_id': 43,      # Mặc định 43
        'admin_name': admin_name,
        'sort_order': 1,         # Mặc định 1
        'swatch_value': None,    # Mặc định NULL
        'is_featured': 0,        # Mặc định 0
        'brand_image': None,     # Mặc định NULL
        'brand_description': None # Mặc định NULL
    }

def process_brands_data(row, attribute_option_id):
    """Xử lý dữ liệu cho bảng brands theo logic mới"""
    # Lấy dữ liệu từ sheet Brands
    image_url = row[0] if len(row) > 0 else ""    # Cột A - Image URL
    brand_name = row[1] if len(row) > 1 else ""   # Cột B - Brand Name
    brand_description = row[2] if len(row) > 2 else ""  # Cột C - Description

    # Kiểm tra dữ liệu hợp lệ
    if not brand_name or brand_name.strip() == "" or brand_name == "Không có":
        return None

    # Làm sạch dữ liệu
    brand_name = brand_name.strip()
    image_url = image_url.strip() if image_url else ""
    brand_description = brand_description.strip() if brand_description else ""

    # Download ảnh và lấy tên file
    brand_image_path = None
    if image_url:
        ensure_directory_exists(IMAGE_DOWNLOAD_PATH)
        filename = download_image(image_url, IMAGE_DOWNLOAD_PATH)
        if filename:
            brand_image_path = f"{IMAGE_URL_PREFIX}{filename}"

    return {
        'attribute_option_id': attribute_option_id,  # ID từ bảng attribute_options
        'brand_name': brand_name,
        'brand_image': brand_image_path,  # Path ảnh đã download
        'brand_description': brand_description,  # Từ cột C - Description
        'outstanding_product_id': None    # Mặc định NULL (sửa tên cột)
    }

def get_data_processor(processor_name):
    """Lấy function xử lý dữ liệu theo tên"""
    processors = {
        'process_brands_data': process_brands_data,
        'process_attribute_options_data': process_attribute_options_data
    }
    return processors.get(processor_name)

def insert_brands_workflow(connection, data_rows):
    """Insert workflow: attribute_options trước, sau đó brands với relationship"""
    print("🚀 BẮT ĐẦU WORKFLOW INSERT 2 BẢNG CÓ QUAN HỆ")
    print("📋 Workflow: attribute_options → brands (với relationship)")

    total_success = 0
    total_failed = 0
    total_skipped = 0
    processed_brands = set()  # Để tránh duplicate brands

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")

                    # Bước 1: Xử lý dữ liệu cho attribute_options
                    attr_data = process_attribute_options_data(row)
                    if not attr_data:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Dữ liệu không hợp lệ")
                        total_skipped += 1
                        continue

                    brand_name = attr_data['admin_name']

                    # Kiểm tra duplicate brand
                    if brand_name in processed_brands:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Brand '{brand_name}' đã tồn tại")
                        total_skipped += 1
                        continue

                    processed_brands.add(brand_name)

                    # Bước 2: Insert vào attribute_options
                    attr_columns = ["attribute_id", "admin_name", "sort_order", "swatch_value", "is_featured", "brand_image", "brand_description"]
                    attr_values = [
                        attr_data['attribute_id'],
                        attr_data['admin_name'],
                        attr_data['sort_order'],
                        attr_data['swatch_value'],
                        attr_data['is_featured'],
                        attr_data['brand_image'],
                        attr_data['brand_description']
                    ]

                    attr_sql = f"INSERT INTO attribute_options ({', '.join(attr_columns)}) VALUES ({', '.join(['%s'] * len(attr_columns))})"
                    cursor.execute(attr_sql, attr_values)

                    # Lấy ID vừa insert
                    attribute_option_id = cursor.lastrowid
                    print(f"  ✅ Inserted attribute_options: ID={attribute_option_id}, admin_name='{brand_name}'")

                    # Bước 3: Xử lý dữ liệu cho brands (với attribute_option_id)
                    brand_data = process_brands_data(row, attribute_option_id)
                    if not brand_data:
                        print(f"  ❌ Lỗi xử lý dữ liệu brands cho dòng {row_index}")
                        total_failed += 1
                        connection.rollback()
                        continue

                    # Bước 4: Insert vào brands
                    brand_columns = ["attribute_option_id", "brand_name", "brand_image", "brand_description", "outstanding_product_id"]
                    brand_values = [
                        brand_data['attribute_option_id'],
                        brand_data['brand_name'],
                        brand_data['brand_image'],
                        brand_data['brand_description'],
                        brand_data['outstanding_product_id']
                    ]

                    brand_sql = f"INSERT INTO brands ({', '.join(brand_columns)}) VALUES ({', '.join(['%s'] * len(brand_columns))})"
                    cursor.execute(brand_sql, brand_values)

                    brand_id = cursor.lastrowid
                    print(f"  ✅ Inserted brands: ID={brand_id}, brand_name='{brand_data['brand_name']}', image='{brand_data['brand_image']}'")

                    # Commit transaction cho từng dòng
                    connection.commit()
                    total_success += 1

                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    connection.rollback()
                    continue

        print(f"\n📊 KẾT QUẢ WORKFLOW:")
        print(f"  ✅ Thành công: {total_success} brands")
        print(f"  ⏭️ Bỏ qua: {total_skipped} brands")
        print(f"  ❌ Thất bại: {total_failed} brands")
        print(f"  📋 Tổng cộng đã xử lý: {total_success * 2} records (attribute_options + brands)")

    except Exception as e:
        print(f"❌ Lỗi workflow: {e}")
        connection.rollback()

def insert_data_to_mysql(connection, data_rows):
    """Insert dữ liệu vào MySQL - chọn workflow phù hợp"""
    if ACTIVE_TABLE == "brands":
        # Sử dụng workflow đặc biệt cho brands (insert cả 2 bảng)
        insert_brands_workflow(connection, data_rows)
    else:
        # Workflow thông thường cho bảng đơn lẻ
        insert_single_table(connection, data_rows)

def insert_single_table(connection, data_rows):
    """Insert dữ liệu vào 1 bảng đơn lẻ"""
    config = CURRENT_CONFIG
    table_name = config["table_name"]
    columns = config["columns"]
    processor_func = get_data_processor(config["processor"])

    print(f"📥 Đang insert dữ liệu vào MySQL table: {table_name}")
    print(f"📋 Columns: {', '.join(columns)}")

    # Tạo SQL query động
    columns_str = ", ".join(columns)
    placeholders = ", ".join(["%s"] * len(columns))
    insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

    total_inserted = 0
    total_failed = 0
    total_skipped = 0
    processed_items = set()  # Để tránh duplicate

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    # Xử lý dữ liệu bằng processor tương ứng
                    processed_data = processor_func(row)

                    if not processed_data:
                        total_skipped += 1
                        continue

                    # Tạo unique key để check duplicate
                    if table_name == "attribute_options":
                        unique_key = processed_data.get('admin_name', '')
                        display_name = unique_key
                    else:
                        unique_key = str(row_index)
                        display_name = f"Row {row_index}"

                    # Kiểm tra duplicate
                    if unique_key in processed_items:
                        total_skipped += 1
                        continue

                    processed_items.add(unique_key)

                    # Tạo record cho insert theo thứ tự columns
                    record = []
                    for column in columns:
                        value = processed_data.get(column, None)
                        record.append(value)

                    cursor.execute(insert_sql, tuple(record))
                    connection.commit()
                    total_inserted += 1
                    print(f"  📦 Dòng {row_index}: {display_name}")

                except Exception as e:
                    print(f"❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    continue

        print(f"\n📊 Tổng cộng cho table {table_name}:")
        print(f"  ✅ Records đã insert: {total_inserted}")
        print(f"  ⏭️ Records đã bỏ qua: {total_skipped}")
        print(f"  ❌ Records thất bại: {total_failed}")

    except Exception as e:
        print(f"❌ Lỗi khi insert dữ liệu vào {table_name}: {e}")
        connection.rollback()

def check_existing_data(connection):
    """Kiểm tra dữ liệu hiện có trong table"""
    print(f"🔍 Kiểm tra dữ liệu hiện có trong table {TABLE_NAME}...")

    try:
        with connection.cursor() as cursor:
            cursor.execute(f"SELECT COUNT(*) as count FROM {TABLE_NAME}")
            result = cursor.fetchone()
            count = result['count']
            print(f"📊 Hiện có {count} records trong table {TABLE_NAME}")

            if count > 0:
                cursor.execute(f"SELECT * FROM {TABLE_NAME} LIMIT 1")
                sample = cursor.fetchone()
                print("📋 Mẫu dữ liệu:")
                for key, value in sample.items():
                    print(f"  - {key}: {value}")
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra dữ liệu: {e}")

def main():
    """Hàm main"""
    print("🚀 BẮT ĐẦU TEST INSERT DATA TỪ GOOGLE SHEETS VÀO MYSQL")
    print("="*60)
    print(f"📊 Cấu hình: Insert tối đa {MAX_ROWS if MAX_ROWS else 'TẤT CẢ'} dòng")
    print(f"📦 Batch size: {BATCH_SIZE}")
    print(f"📁 Database: {DB_CONFIG['database']}")
    print(f"📋 Active Table: {ACTIVE_TABLE}")
    print(f"📋 Table Name: {TABLE_NAME}")
    print(f"📋 Columns: {', '.join(CURRENT_CONFIG['columns'])}")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối MySQL
        connection = connect_to_mysql()

        # Kiểm tra table brands
        create_table_if_not_exists(connection)

        # Kiểm tra dữ liệu hiện có
        check_existing_data(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Insert dữ liệu vào MySQL
        insert_data_to_mysql(connection, data_rows)

        # Kiểm tra lại sau khi insert
        print("\n🔍 Kiểm tra lại sau khi insert:")
        check_existing_data(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT TEST INSERT DATA!")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
