import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import time
import sys
import os

# ===== CẤU HÌNH =====
# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "Medicine"  # Sheet Medicine như file insert_adminer
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
ROW_NUMBER =   # 🎯 KIỂM SOÁT SỐ HÀNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
START_ROW = 2  # Bắt đầu từ dòng nào (2 = bỏ qua header)

# ===== CẤU HÌNH ATTRIBUTE_OPTIONS =====
ATTRIBUTE_ID = 45  # attribute_id mặc định
SORT_ORDER_START = 4  # sort_order bắt đầu từ 4
COLUMN_INDEX = 9  # Cột J (index 9) - Phân loại sản phẩm

# ===== CẤU HÌNH CÁC BẢNG CẦN INSERT =====
TABLES_TO_INSERT = [
    "attribute_options"
]

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print("✅ Đã kết nối Google Sheets thành công!")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    print("🔗 Đang kết nối MySQL Docker (Adminer)...")
    print(f"🖥️ Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 User: {DB_CONFIG['user']}")
    print(f"🗄️ Database: {DB_CONFIG['database']}")

    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL Docker thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL Docker: {e}")
        print("💡 Thử các cách sau:")
        print("   - Đảm bảo Docker đang chạy")
        print("   - Kiểm tra port 3307 có đúng không")
        print("   - Thử thay 'localhost' bằng 'mysql' hoặc '127.0.0.1'")
        print("   - Kiểm tra Adminer: http://localhost:8080")
        sys.exit(1)

def check_tables_exist(connection):
    """Kiểm tra bảng attribute_options trong Docker MySQL"""
    table_name = "attribute_options"
    print(f"🔧 Kiểm tra table {table_name} trong Docker MySQL...")

    # Kiểm tra xem table đã tồn tại chưa
    check_table_sql = f"""
    SELECT COUNT(*) as count
    FROM information_schema.tables
    WHERE table_schema = '{DB_CONFIG['database']}'
    AND table_name = '{table_name}'
    """

    try:
        with connection.cursor() as cursor:
            cursor.execute(check_table_sql)
            result = cursor.fetchone()

            if result['count'] > 0:
                print(f"✅ Table {table_name} đã tồn tại trong Docker!")

                # Kiểm tra cấu trúc table
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                print(f"📋 Cấu trúc table {table_name}:")
                for col in columns:
                    print(f"  - {col['Field']}: {col['Type']}")
                print()
            else:
                print(f"❌ Table {table_name} chưa tồn tại trong Docker!")
                print("💡 Vui lòng tạo table trong Adminer trước: http://localhost:8080")
                sys.exit(1)

    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra table {table_name}: {e}")
        sys.exit(1)

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")

    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()

        # Tính toán số dòng thực tế có data (bỏ qua header)
        total_rows_with_header = len(all_data)
        total_data_rows = total_rows_with_header - (START_ROW - 1)

        print(f"📊 Sheet có tổng cộng {total_rows_with_header} dòng (bao gồm header)")
        print(f"📊 Số dòng data thực tế: {total_data_rows} dòng")

        # Bỏ qua header
        data_rows = all_data[START_ROW-1:]

        # Giới hạn số lượng dòng nếu cần
        if ROW_NUMBER:
            if ROW_NUMBER > total_data_rows:
                print(f"⚠️  ROW_NUMBER ({ROW_NUMBER}) lớn hơn số dòng data thực tế ({total_data_rows})")
                print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data có sẵn")
            else:
                print(f"📋 Giới hạn xử lý {ROW_NUMBER} dòng đầu tiên từ {total_data_rows} dòng data")
                data_rows = data_rows[:ROW_NUMBER]
        else:
            print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data")

        actual_rows_to_process = len(data_rows)
        print(f"✅ Đã lấy {actual_rows_to_process} dòng dữ liệu từ Google Sheets để xử lý")

        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        sys.exit(1)

def get_column_value(row, column_index):
    """Lấy giá trị từ cột theo index"""
    if len(row) > column_index:
        return row[column_index].strip() if row[column_index] else ""
    return ""

def check_unit_type_exists(connection, admin_name):
    """Kiểm tra đơn vị (admin_name) đã tồn tại trong bảng attribute_options chưa với attribute_id=45"""
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT id, admin_name, sort_order
            FROM attribute_options
            WHERE attribute_id = %s AND admin_name = %s
            """
            cursor.execute(sql, (ATTRIBUTE_ID, admin_name))
            result = cursor.fetchone()

            if result:
                print(f"      ⚠️ Đơn vị '{admin_name}' đã tồn tại trong bảng attribute_options")
                print(f"          📋 ID={result['id']}, attribute_id={ATTRIBUTE_ID}, sort_order={result['sort_order']}")
                return result['id']
            else:
                return None

    except Exception as e:
        print(f"  ❌ Lỗi khi check đơn vị '{admin_name}': {e}")
        return None

def get_next_sort_order(connection):
    """Lấy sort_order tiếp theo"""
    try:
        with connection.cursor() as cursor:
            sql = "SELECT MAX(sort_order) as max_sort FROM attribute_options WHERE attribute_id = %s"
            cursor.execute(sql, (ATTRIBUTE_ID,))
            result = cursor.fetchone()

            if result and result['max_sort'] is not None:
                return result['max_sort'] + 1
            else:
                return SORT_ORDER_START
    except Exception as e:
        print(f"  ❌ Lỗi khi lấy sort_order: {e}")
        return SORT_ORDER_START

def insert_unit_type(connection, admin_name, sort_order):
    """Insert 1 đơn vị vào bảng attribute_options với attribute_id=45"""
    try:
        with connection.cursor() as cursor:
            # Insert vào attribute_options
            sql = """
            INSERT INTO attribute_options (attribute_id, admin_name, sort_order, swatch_value, is_featured, brand_image, brand_description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            values = (ATTRIBUTE_ID, admin_name, sort_order, None, 0, None, None)
            cursor.execute(sql, values)

            # Lấy ID vừa insert
            unit_type_id = cursor.lastrowid
            print(f"        ✅ Inserted attribute_options: ID={unit_type_id}")
            print(f"            📋 attribute_id={ATTRIBUTE_ID}, admin_name='{admin_name}', sort_order={sort_order}")
            print(f"            📋 swatch_value=NULL, is_featured=0, brand_image=NULL, brand_description=NULL")

            return unit_type_id

    except Exception as e:
        print(f"    ❌ Lỗi khi insert đơn vị '{admin_name}': {e}")
        raise e

def process_unit_types_from_row(row):
    """Xử lý cột J (phân loại sản phẩm) và tách theo dấu phẩy"""
    # Lấy dữ liệu từ cột J
    unit_types_string = get_column_value(row, COLUMN_INDEX)

    if not unit_types_string:
        return []

    # Bỏ qua nếu giá trị là "Không có"
    if unit_types_string.strip().lower() == "không có":
        return []

    # Tách theo dấu phẩy và làm sạch
    unit_types = [unit.strip() for unit in unit_types_string.split(',') if unit.strip()]

    return unit_types

def insert_unit_types_workflow(connection, data_rows):
    """Insert workflow: Đọc cột J → Tách theo dấu phẩy → Insert vào attribute_options"""
    print("🚀 BẮT ĐẦU WORKFLOW INSERT UNIT TYPES (DOCKER)")
    print("📋 Workflow: Đọc cột J → Tách theo dấu phẩy → Insert attribute_options")

    total_success = 0
    total_failed = 0
    total_skipped = 0
    processed_unit_types = set()  # Để tránh duplicate unit types

    # Lấy sort_order bắt đầu
    current_sort_order = get_next_sort_order(connection)
    print(f"📊 Sort order bắt đầu từ: {current_sort_order}")

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")

                    # Lấy dữ liệu từ cột J
                    if len(row) <= COLUMN_INDEX:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Không có dữ liệu cột J")
                        total_skipped += 1
                        continue

                    unit_types = process_unit_types_from_row(row)

                    if not unit_types:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Cột J rỗng hoặc 'Không có'")
                        total_skipped += 1
                        continue

                    print(f"  📝 Dữ liệu cột J: {unit_types}")

                    # Xử lý từng đơn vị (unit_type)
                    row_inserted = 0
                    for unit_type in unit_types:
                        print(f"    🔍 Kiểm tra đơn vị '{unit_type}'...")

                        # Bước 1: Kiểm tra duplicate trong session hiện tại
                        if unit_type in processed_unit_types:
                            print(f"      ⏭️ Đơn vị '{unit_type}' đã được xử lý trong session này")
                            continue

                        # Bước 2: Kiểm tra duplicate trong database (attribute_id=45 + admin_name)
                        existing_id = check_unit_type_exists(connection, unit_type)
                        if existing_id:
                            print(f"      ⏭️ Bỏ qua: Đơn vị '{unit_type}' đã tồn tại trong database")
                            print(f"          💡 Không cần insert lại đơn vị này")
                            processed_unit_types.add(unit_type)
                            continue

                        # Bước 3: Insert đơn vị mới
                        print(f"      🆕 Tạo mới đơn vị '{unit_type}' với sort_order={current_sort_order}")
                        insert_unit_type(connection, unit_type, current_sort_order)

                        processed_unit_types.add(unit_type)
                        current_sort_order += 1
                        row_inserted += 1

                    if row_inserted > 0:
                        # Commit transaction cho từng dòng
                        connection.commit()
                        total_success += 1
                        print(f"  ✅ Đã xử lý {row_inserted} unit_types từ dòng {row_index}")
                    else:
                        total_skipped += 1

                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    connection.rollback()
                    continue

        print(f"\n📊 KẾT QUẢ WORKFLOW UNIT TYPES (DOCKER):")
        print(f"  ✅ Thành công: {total_success} dòng")
        print(f"  ⏭️ Bỏ qua: {total_skipped} dòng")
        print(f"  ❌ Thất bại: {total_failed} dòng")
        print(f"  📋 Tổng unit_types unique đã xử lý: {len(processed_unit_types)}")

    except Exception as e:
        print(f"❌ Lỗi workflow: {e}")
        connection.rollback()

def check_existing_data(connection):
    """Kiểm tra dữ liệu hiện có trong table attribute_options"""
    table_name = "attribute_options"
    print(f"🔍 Kiểm tra dữ liệu hiện có trong table {table_name} (Docker)...")

    try:
        with connection.cursor() as cursor:
            # Tổng số records
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
            result = cursor.fetchone()
            total_count = result['count']

            # Số records với attribute_id = 45
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name} WHERE attribute_id = %s", (ATTRIBUTE_ID,))
            result = cursor.fetchone()
            attribute_count = result['count']

            print(f"📊 Hiện có {total_count} records trong table {table_name}")
            print(f"📊 Hiện có {attribute_count} records với attribute_id={ATTRIBUTE_ID}")

            # Hiển thị một vài mẫu
            if attribute_count > 0:
                cursor.execute(f"SELECT * FROM {table_name} WHERE attribute_id = %s LIMIT 3", (ATTRIBUTE_ID,))
                samples = cursor.fetchall()
                print("📋 Mẫu dữ liệu:")
                for sample in samples:
                    print(f"  - ID={sample['id']}, admin_name='{sample['admin_name']}', sort_order={sample['sort_order']}")
            print()
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra dữ liệu {table_name}: {e}")

def main():
    """Hàm main cho Unit Types workflow"""
    print("🚀 BẮT ĐẦU INSERT UNIT TYPES VÀO DOCKER MYSQL (ADMINER)")
    print("="*60)
    print(f"📊 Cấu hình: Insert tối đa {ROW_NUMBER if ROW_NUMBER else 'TẤT CẢ'} dòng")
    print(f"🐳 Docker Database: {DB_CONFIG['database']}")
    print(f"🖥️ Docker Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"📋 Active Workflow: Unit Types từ cột J")
    print(f"📋 Table: attribute_options")
    print(f"📋 Attribute ID: {ATTRIBUTE_ID}")
    print(f"📋 Sort Order Start: {SORT_ORDER_START}")
    print(f"🌐 Adminer URL: http://localhost:8080")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối Docker MySQL
        connection = connect_to_mysql()

        # Kiểm tra table trong Docker
        check_tables_exist(connection)

        # Kiểm tra dữ liệu hiện có
        check_existing_data(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Insert dữ liệu vào Docker MySQL
        insert_unit_types_workflow(connection, data_rows)

        # Kiểm tra lại sau khi insert
        print("\n🔍 Kiểm tra lại sau khi insert:")
        check_existing_data(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT INSERT UNIT TYPES VÀO DOCKER!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
