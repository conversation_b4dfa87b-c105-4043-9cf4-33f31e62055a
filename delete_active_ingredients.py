import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import sys

# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "Medicine"  # Sheet Medicine như file insert_adminer
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
ROW_NUMBER = 1  # 🎯 KIỂM SOÁT SỐ HÀNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
START_ROW = 2  # Bắt đầu từ dòng nào (2 = bỏ qua header)
COLUMN_INDEX = 10  # Cột K (index 10) - Hoạt chất
ATTRIBUTE_ID = 44  # attribute_id cho attribute_options

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print("✅ Đã kết nối Google Sheets thành công!")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    print("🔗 Đang kết nối MySQL Docker (Adminer)...")
    print(f"🖥️ Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 User: {DB_CONFIG['user']}")
    print(f"🗄️ Database: {DB_CONFIG['database']}")

    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL Docker thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL Docker: {e}")
        sys.exit(1)

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")

    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()

        # Bỏ qua header
        data_rows = all_data[START_ROW-1:]

        # Giới hạn số lượng dòng nếu cần
        if ROW_NUMBER:
            data_rows = data_rows[:ROW_NUMBER]

        print(f"✅ Đã lấy {len(data_rows)} dòng dữ liệu từ Google Sheets để xử lý")
        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        sys.exit(1)

def get_column_value(row, column_index):
    """Lấy giá trị từ cột theo index"""
    if len(row) > column_index:
        return row[column_index].strip() if row[column_index] else ""
    return ""

def process_active_ingredients_from_row(row):
    """Xử lý cột K (hoạt chất) và tách theo dấu chấm phẩy"""
    # Lấy dữ liệu từ cột K
    ingredients_string = get_column_value(row, COLUMN_INDEX)

    if not ingredients_string:
        return []

    # Bỏ qua nếu giá trị là "Không có"
    if ingredients_string.strip().lower() == "không có":
        return []

    # Tách theo dấu chấm phẩy và làm sạch
    ingredients = [ingredient.strip() for ingredient in ingredients_string.split(';') if ingredient.strip()]

    return ingredients

def delete_active_ingredient_by_name(connection, ingredient_name):
    """Xóa hoạt chất theo tên từ cả 2 bảng"""
    try:
        with connection.cursor() as cursor:
            deleted_total = 0

            # Xóa từ active_ingredients
            cursor.execute("DELETE FROM active_ingredients WHERE name = %s", (ingredient_name,))
            deleted_ingredients = cursor.rowcount
            if deleted_ingredients > 0:
                print(f"        ✅ Đã xóa {deleted_ingredients} record từ active_ingredients")
                deleted_total += deleted_ingredients

            # Xóa từ attribute_options
            cursor.execute("DELETE FROM attribute_options WHERE attribute_id = %s AND admin_name = %s", (ATTRIBUTE_ID, ingredient_name))
            deleted_options = cursor.rowcount
            if deleted_options > 0:
                print(f"        ✅ Đã xóa {deleted_options} record từ attribute_options")
                deleted_total += deleted_options

            if deleted_total == 0:
                print(f"        ⚠️ Không tìm thấy hoạt chất '{ingredient_name}' trong database")

            return deleted_total

    except Exception as e:
        print(f"        ❌ Lỗi khi xóa hoạt chất '{ingredient_name}': {e}")
        raise e

def delete_active_ingredients_workflow(connection, data_rows):
    """Delete workflow: Đọc cột K → Tách theo dấu chấm phẩy → Xóa khỏi 2 bảng"""
    print("🗑️ BẮT ĐẦU WORKFLOW DELETE ACTIVE INGREDIENTS (DOCKER)")
    print("📋 Workflow: Đọc cột K → Tách theo dấu chấm phẩy → Delete từ 2 bảng")

    total_success = 0
    total_failed = 0
    total_skipped = 0
    processed_ingredients = set()  # Để tránh duplicate ingredients

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")

                    # Lấy dữ liệu từ cột K
                    if len(row) <= COLUMN_INDEX:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Không có dữ liệu cột K")
                        total_skipped += 1
                        continue

                    ingredients = process_active_ingredients_from_row(row)

                    if not ingredients:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Cột K rỗng hoặc 'Không có'")
                        total_skipped += 1
                        continue

                    print(f"  📝 Dữ liệu cột K: {ingredients}")

                    # Xử lý từng hoạt chất
                    row_deleted = 0
                    for ingredient in ingredients:
                        print(f"    🗑️ Xóa hoạt chất '{ingredient}'...")

                        # Kiểm tra duplicate trong session
                        if ingredient in processed_ingredients:
                            print(f"      ⏭️ Hoạt chất '{ingredient}' đã xử lý trong session này")
                            continue

                        # Xóa hoạt chất
                        deleted_count = delete_active_ingredient_by_name(connection, ingredient)

                        processed_ingredients.add(ingredient)
                        if deleted_count > 0:
                            row_deleted += deleted_count

                    if row_deleted > 0:
                        # Commit transaction cho từng dòng
                        connection.commit()
                        total_success += 1
                        print(f"  ✅ Đã xóa {row_deleted} records từ dòng {row_index}")
                    else:
                        total_skipped += 1

                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    connection.rollback()
                    continue

        print(f"\n📊 KẾT QUẢ WORKFLOW DELETE ACTIVE INGREDIENTS (DOCKER):")
        print(f"  ✅ Thành công: {total_success} dòng")
        print(f"  ⏭️ Bỏ qua: {total_skipped} dòng")
        print(f"  ❌ Thất bại: {total_failed} dòng")
        print(f"  📋 Tổng hoạt chất unique đã xử lý: {len(processed_ingredients)}")

    except Exception as e:
        print(f"❌ Lỗi workflow: {e}")
        connection.rollback()

def check_data_before_after(connection):
    """Kiểm tra dữ liệu trước và sau khi xóa"""
    tables = [
        ("active_ingredients", "SELECT COUNT(*) as count FROM active_ingredients"),
        ("attribute_options (attr_id=44)", "SELECT COUNT(*) as count FROM attribute_options WHERE attribute_id = 44")
    ]

    for table_name, query in tables:
        try:
            with connection.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                count = result['count']
                print(f"📊 {table_name}: {count} records")
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra {table_name}: {e}")

def main():
    """Hàm main để xóa dữ liệu"""
    print("🗑️ BẮT ĐẦU XÓA ACTIVE INGREDIENTS THEO GOOGLE SHEETS")
    print("="*60)
    print(f"📊 Cấu hình: Xử lý tối đa {ROW_NUMBER if ROW_NUMBER else 'TẤT CẢ'} dòng")
    print("🎯 Mục tiêu: Đọc cột K và xóa hoạt chất theo tên")
    print("📋 Tables: active_ingredients, attribute_options")
    print("🌐 Adminer URL: http://localhost:8080")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối Docker MySQL
        connection = connect_to_mysql()

        # Kiểm tra dữ liệu trước khi xóa
        print("\n🔍 Dữ liệu TRƯỚC khi xóa:")
        check_data_before_after(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Xóa dữ liệu theo workflow
        delete_active_ingredients_workflow(connection, data_rows)

        # Kiểm tra dữ liệu sau khi xóa
        print("\n🔍 Dữ liệu SAU khi xóa:")
        check_data_before_after(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT XÓA DỮ LIỆU!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
