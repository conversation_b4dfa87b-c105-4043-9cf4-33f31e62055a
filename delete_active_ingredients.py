import pymysql
import sys

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    print("🔗 Đang kết nối MySQL Docker (Adminer)...")
    print(f"🖥️ Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 User: {DB_CONFIG['user']}")
    print(f"🗄️ Database: {DB_CONFIG['database']}")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL Docker thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL Docker: {e}")
        sys.exit(1)

def delete_recent_active_ingredients(connection):
    """Xóa các hoạt chất vừa insert (ID=9, 10)"""
    print("🗑️ Đang xóa dữ liệu active_ingredients vừa insert...")
    
    try:
        with connection.cursor() as cursor:
            # Xóa từ active_ingredients (ID=9, 10)
            cursor.execute("DELETE FROM active_ingredients WHERE id IN (9, 10)")
            deleted_ingredients = cursor.rowcount
            print(f"✅ Đã xóa {deleted_ingredients} records từ active_ingredients")
            
            # Xóa từ attribute_options (ID=113, 114)
            cursor.execute("DELETE FROM attribute_options WHERE id IN (113, 114)")
            deleted_options = cursor.rowcount
            print(f"✅ Đã xóa {deleted_options} records từ attribute_options")
            
            # Commit changes
            connection.commit()
            print("✅ Đã commit thay đổi")
            
    except Exception as e:
        print(f"❌ Lỗi khi xóa dữ liệu: {e}")
        connection.rollback()

def check_data_before_after(connection):
    """Kiểm tra dữ liệu trước và sau khi xóa"""
    tables = [
        ("active_ingredients", "SELECT COUNT(*) as count FROM active_ingredients"),
        ("attribute_options (attr_id=44)", "SELECT COUNT(*) as count FROM attribute_options WHERE attribute_id = 44")
    ]
    
    for table_name, query in tables:
        try:
            with connection.cursor() as cursor:
                cursor.execute(query)
                result = cursor.fetchone()
                count = result['count']
                print(f"📊 {table_name}: {count} records")
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra {table_name}: {e}")

def main():
    """Hàm main để xóa dữ liệu"""
    print("🗑️ BẮT ĐẦU XÓA ACTIVE INGREDIENTS VỪA INSERT")
    print("="*60)
    print("🎯 Mục tiêu: Xóa Dichlorobenzyl Alcohol và Amylmetacresol")
    print("📋 Tables: active_ingredients, attribute_options")
    print("🌐 Adminer URL: http://localhost:8080")
    print("="*60)
    
    try:
        # Kết nối Docker MySQL
        connection = connect_to_mysql()
        
        # Kiểm tra dữ liệu trước khi xóa
        print("\n🔍 Dữ liệu TRƯỚC khi xóa:")
        check_data_before_after(connection)
        
        # Xóa dữ liệu
        delete_recent_active_ingredients(connection)
        
        # Kiểm tra dữ liệu sau khi xóa
        print("\n🔍 Dữ liệu SAU khi xóa:")
        check_data_before_after(connection)
        
        # Đóng kết nối
        connection.close()
        
        print("\n" + "="*60)
        print("🎉 HOÀN TẤT XÓA DỮ LIỆU!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)
        
    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
