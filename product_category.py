import gspread
from oauth2client.service_account import ServiceAccountCredentials
import requests
from bs4 import BeautifulSoup
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

# ===== CẤU HÌNH =====
START_ROW = 1685  # Dòng bắt đầu xử lý (2 = bỏ qua header, bắt đầu từ dòng đầu tiên có dữ liệu)
MAX_ROWS =  400 # Số dòng tối đa muốn xử lý (None = tất cả dòng)
DELAY_BETWEEN_REQUESTS = 2  # Giây delay gi<PERSON><PERSON> cá<PERSON> request
REQUEST_TIMEOUT = 30  # Timeout cho mỗi request
USE_SELENIUM = True  # Sử dụng Selenium thay vì requests

# Global driver variable
driver = None

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    
    max_retries = 3
    for attempt in range(max_retries):
        try:
            print(f"  🔄 Thử kết nối lần {attempt + 1}/{max_retries}...")
            creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
            client = gspread.authorize(creds)
            
            # Thêm timeout cho kết nối
            import socket
            socket.setdefaulttimeout(30)
            
            sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("Medicine")
            print("✅ Đã kết nối Google Sheets thành công!")
            return sheet
            
        except Exception as e:
            print(f"  ❌ Lần thử {attempt + 1} thất bại: {str(e)[:100]}...")
            if attempt < max_retries - 1:
                print(f"  ⏳ Đợi 5 giây trước khi thử lại...")
                time.sleep(5)
            else:
                print(f"  💥 Đã thử {max_retries} lần nhưng không thể kết nối!")
                raise e

def extract_breadcrumb_categories(html_content):
    """Trích xuất categories từ breadcrumb HTML"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # Pattern 1: ul với class chứa "flex items-center" và "text-neutral-600"
        breadcrumb = soup.find('ul', class_=lambda x: x and 'flex' in str(x) and 'items-center' in str(x) and 'text-neutral-600' in str(x))

        # Pattern 2: Tìm breadcrumb bằng cách tìm "Trang chủ"
        if not breadcrumb:
            home_links = soup.find_all('a', string=lambda text: text and 'Trang chủ' in text)
            for home_link in home_links:
                parent_ul = home_link.find_parent('ul')
                if parent_ul:
                    breadcrumb = parent_ul
                    break

        # Pattern 3: Tìm nav với breadcrumb
        if not breadcrumb:
            nav_breadcrumb = soup.find('nav', class_=lambda x: x and 'breadcrumb' in str(x).lower())
            if nav_breadcrumb:
                breadcrumb = nav_breadcrumb.find('ul')

        # Pattern 4: Tìm bất kỳ ul nào có nhiều links
        if not breadcrumb:
            all_uls = soup.find_all('ul')
            for ul in all_uls:
                links = ul.find_all('a')
                if len(links) >= 2:  # Có ít nhất 2 links
                    breadcrumb = ul
                    break

        if not breadcrumb:
            return None

        # Tìm tất cả thẻ a trong breadcrumb
        links = breadcrumb.find_all('a')
        categories = []

        for link in links:
            text = link.get_text(strip=True)
            if text and text != "Trang chủ":  # Loại bỏ "Trang chủ"
                categories.append(text)

        # Tìm text cuối cùng không có link (category hiện tại)
        last_li = breadcrumb.find_all('li')[-1] if breadcrumb.find_all('li') else None
        if last_li:
            # Tìm span cuối cùng không chứa thẻ a
            spans = last_li.find_all('span')
            for span in spans:
                if not span.find('a') and span.get_text(strip=True):
                    text = span.get_text(strip=True)
                    if text and text not in categories:
                        categories.append(text)
                        break

        return "; ".join(categories) if categories else None

    except Exception as e:
        print(f"    ❌ Lỗi parse HTML: {str(e)}")
        return None

def setup_selenium_driver():
    """Thiết lập Selenium driver"""
    global driver
    try:
        print("🚀 Đang khởi tạo Chrome driver...")

        chrome_options = Options()
        chrome_options.add_argument("--headless")  # Chạy ẩn
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

        # Sử dụng webdriver-manager để tự động tải ChromeDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(REQUEST_TIMEOUT)

        print("✅ Chrome driver đã sẵn sàng!")
        return True

    except Exception as e:
        print(f"❌ Lỗi khởi tạo driver: {str(e)}")
        return False

def cleanup_driver():
    """Dọn dẹp driver"""
    global driver
    if driver:
        try:
            driver.quit()
            print("🧹 Đã đóng Chrome driver")
        except:
            pass

def fetch_page_content_selenium(url):
    """Lấy nội dung trang web bằng Selenium"""
    global driver
    try:
        if not driver:
            if not setup_selenium_driver():
                return None

        driver.get(url)

        # Đợi breadcrumb load
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "ul"))
            )
        except TimeoutException:
            pass  # Không có breadcrumb cũng OK

        return driver.page_source

    except Exception as e:
        print(f"    ❌ Lỗi fetch URL với Selenium: {str(e)}")
        return None

def fetch_page_content(url):
    """Lấy nội dung trang web"""
    if USE_SELENIUM:
        return fetch_page_content_selenium(url)
    else:
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }

            response = requests.get(url, headers=headers, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()
            return response.text

        except Exception as e:
            print(f"    ❌ Lỗi fetch URL: {str(e)}")
            return None

def main():
    """Hàm main"""
    print("🏷️ BẮT ĐẦU TRÍCH XUẤT PRODUCT CATEGORIES")
    print("="*60)
    print(f"📊 Bắt đầu từ dòng: {START_ROW}")
    print(f"📊 Tối đa xử lý: {MAX_ROWS if MAX_ROWS else 'TẤT CẢ'} dòng")
    print(f"⏱️ Delay giữa requests: {DELAY_BETWEEN_REQUESTS}s")
    print(f"🤖 Sử dụng Selenium: {USE_SELENIUM}")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Lấy dữ liệu từ cột A (URLs)
        print("\n📋 Đang lấy dữ liệu từ cột A (URLs)...")
        # Lấy tất cả giá trị từ cột A
        column_a_values = sheet.col_values(1)  # Cột A = cột 1

        print(f"📊 Tìm thấy {len(column_a_values)} ô trong cột A")

        # Lấy URLs từ START_ROW trở đi
        if len(column_a_values) >= START_ROW:
            urls = column_a_values[START_ROW-1:]  # START_ROW-1 vì index bắt đầu từ 0
        else:
            urls = []

        print(f"📊 Có {len(urls)} URL để xử lý (từ dòng {START_ROW})")

        total_processed = 0
        total_success = 0
        total_failed = 0
        rows_processed = 0

        for i, url in enumerate(urls):
            if MAX_ROWS and rows_processed >= MAX_ROWS:
                print(f"\n🛑 Đã xử lý {MAX_ROWS} dòng, dừng xử lý")
                break

            rows_processed += 1
            row_index = START_ROW + i  # Tính row_index thực tế

            # Kiểm tra URL có hợp lệ không
            if not url or url.strip() == "" or not url.startswith('http'):
                print(f"\n⚠️ Dòng {rows_processed}/{MAX_ROWS if MAX_ROWS else '∞'} - Hàng {row_index}: URL không hợp lệ, bỏ qua")
                continue

            url = url.strip()
            print(f"\n📄 Dòng {rows_processed}/{MAX_ROWS if MAX_ROWS else '∞'} - Hàng {row_index}")
            print(f"  🔗 URL: {url[:80]}...")

            # Clear giá trị cột G trước khi insert
            print(f"  🧹 Xóa giá trị cũ ở cột G...")
            sheet.update_cell(row_index, 7, "")  # Cột G = cột 7

            # Fetch nội dung trang
            print(f"  📥 Đang tải nội dung trang...")
            html_content = fetch_page_content(url)

            if not html_content:
                print(f"  ❌ Không thể tải nội dung trang")
                total_failed += 1
                time.sleep(DELAY_BETWEEN_REQUESTS)
                continue

            # Trích xuất categories từ breadcrumb
            print(f"  🔍 Đang trích xuất categories...")
            categories = extract_breadcrumb_categories(html_content)

            if categories:
                print(f"  ✅ Tìm thấy categories: {categories}")

                # Cập nhật vào cột G
                print(f"  💾 Đang cập nhật vào cột G...")
                sheet.update_cell(row_index, 7, categories)  # Cột G = cột 7
                print(f"  ✅ Đã cập nhật thành công!")

                total_success += 1
            else:
                print(f"  ⚠️ Không tìm thấy breadcrumb categories")
                total_failed += 1

            total_processed += 1

            # Delay giữa các requests
            if DELAY_BETWEEN_REQUESTS > 0:
                time.sleep(DELAY_BETWEEN_REQUESTS)

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT TRÍCH XUẤT CATEGORIES!")
        print("="*60)
        print(f"📋 Dòng đã xử lý: {rows_processed}")
        print(f"📦 URL đã xử lý: {total_processed}")
        print(f"✅ Thành công: {total_success}")
        print(f"❌ Thất bại: {total_failed}")
        print(f"🔗 Link Google Sheets: https://docs.google.com/spreadsheets/d/1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi khi xử lý dữ liệu: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup driver
        cleanup_driver()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Script bị dừng bởi người dùng (Ctrl+C)")
        cleanup_driver()
    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        cleanup_driver()
