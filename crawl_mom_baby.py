from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import time
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import requests
import atexit
import signal
import sys

# Global driver variable để cleanup
driver = None

def cleanup_driver():
    """Đóng browser và cleanup resources"""
    global driver
    if driver:
        try:
            print("\n🧹 Đang cleanup browser...")
            driver.quit()
            print("✅ Browser đã được đóng")
        except Exception as e:
            print(f"⚠️ Lỗi khi đóng browser: {e}")
        finally:
            driver = None

def signal_handler(signum, frame):
    """Xử lý signal khi script bị dừng"""
    print(f"\n⚠️ Nhận signal {signum}, đang cleanup...")
    cleanup_driver()
    sys.exit(0)

# Đăng ký signal handlers
signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
signal.signal(signal.SIGTERM, signal_handler)  # Terminate
atexit.register(cleanup_driver)  # Cleanup khi script kết thúc

# Hàm kết nối Google Sheets
def connect_to_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    # Sử dụng file JSON mới
    creds = ServiceAccountCredentials.from_json_keyfile_name("medical-crawl-2024-013b6faaa588.json", scope)
    client = gspread.authorize(creds)
    # Sử dụng ID của Google Sheet từ URL - THAY ĐỔI SHEET THÀNH "MomAndBaby"
    sheet = client.open_by_key("1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8").worksheet("MomAndBaby")
    return sheet

def ensure_sheet_columns(sheet, required_columns):
    """Đảm bảo sheet có đủ số cột cần thiết"""
    try:
        current_cols = sheet.col_count
        if current_cols < required_columns:
            sheet.add_cols(required_columns - current_cols)
            print(f"✅ Đã mở rộng sheet từ {current_cols} lên {required_columns} cột")
    except Exception as e:
        print(f"⚠️ Không thể mở rộng sheet: {e}")

def get_column_letter(index):
    """Chuyển đổi index cột thành tên cột (A, B, ..., Z, AA, AB, ...)"""
    if index < 26:
        return chr(ord('A') + index)
    else:
        # AA, AB, AC, ... (index 26, 27, 28, ...)
        first_letter = chr(ord('A') + (index // 26) - 1)
        second_letter = chr(ord('A') + (index % 26))
        return first_letter + second_letter

def discover_all_categories(driver):
    """Tự động khám phá tất cả categories từ trang Mẹ và bé Pharmacity"""
    print("\n🔍 KHÁM PHÁ TẤT CẢ CATEGORIES TỪ TRANG MẸ VÀ BÉ")
    print("="*60)

    # Bắt đầu từ trang Mẹ và bé
    main_mom_baby_url = "https://www.pharmacity.vn/me-va-be"
    print(f"📄 Vào trang Mẹ và bé: {main_mom_baby_url}")
    driver.get(main_mom_baby_url)
    time.sleep(5)

    all_categories = []

    # Lấy HTML của trang
    soup = BeautifulSoup(driver.page_source, "html.parser")

    # Tìm tất cả các category cha (4 cái chính)
    print("\n🔍 Bước 1: Tìm các category cha...")

    # Tìm các thẻ có thể chứa category links
    category_containers = soup.find_all(['div', 'section', 'nav'], class_=lambda x: x and any(keyword in x.lower() for keyword in ['category', 'menu', 'nav', 'grid']))

    main_categories = []

    # Tìm links có pattern mẹ và bé
    all_links = soup.find_all('a', href=True)
    for link in all_links:
        href = link.get('href')
        text = link.get_text(strip=True)

        if href and text:
            # Kiểm tra pattern cho category mẹ và bé
            mom_baby_patterns = [
                'me-va-be',
                'sua-bot',
                'ta-bim',
                'cham-soc-be',
                'cham-soc-me',
                'do-choi',
                'thuc-pham-bo-sung',
                'vitamin-cho-be',
                'sua-cong-thuc'
            ]

            if any(pattern in href for pattern in mom_baby_patterns):
                if href.startswith('/'):
                    full_url = 'https://www.pharmacity.vn' + href
                elif href.startswith('https://www.pharmacity.vn'):
                    full_url = href
                else:
                    continue

                # Loại bỏ query parameters
                if '?' in full_url:
                    full_url = full_url.split('?')[0]

                if full_url not in [cat[1] for cat in main_categories]:
                    main_categories.append((text, full_url))
                    print(f"  ✅ Tìm thấy category cha: {text} → {full_url}")

    print(f"\n📊 Tìm thấy {len(main_categories)} category cha")

    # Bước 2: Với mỗi category cha, tìm subcategories
    print(f"\n🔍 Bước 2: Tìm subcategories cho từng category cha...")

    for main_name, main_url in main_categories:
        print(f"\n🏷️ Phân tích category: {main_name}")
        print(f"📄 URL: {main_url}")

        try:
            driver.get(main_url)
            time.sleep(3)

            sub_soup = BeautifulSoup(driver.page_source, "html.parser")

            # Tìm subcategories bằng nhiều cách
            subcategories = []

            # Cách 1: Tìm trong các thẻ có class chứa category/grid
            category_grids = sub_soup.find_all(['div', 'section'], class_=lambda x: x and any(keyword in x.lower() for keyword in ['grid', 'category', 'product-category']))

            for grid in category_grids:
                grid_links = grid.find_all('a', href=True)
                for link in grid_links:
                    href = link.get('href')
                    text = link.get_text(strip=True)

                    if href and text and len(text) > 3:
                        if href.startswith('/'):
                            full_url = 'https://www.pharmacity.vn' + href
                        elif href.startswith('https://www.pharmacity.vn'):
                            full_url = href
                        else:
                            continue

                        # Loại bỏ query parameters
                        if '?' in full_url:
                            full_url = full_url.split('?')[0]

                        # Kiểm tra xem có phải subcategory mẹ và bé không
                        if (any(keyword in full_url for keyword in ['me-va-be', 'sua-', 'ta-', 'cham-soc', 'do-choi', 'vitamin-cho-be']) and
                            full_url != main_url and
                            full_url not in [sub[1] for sub in subcategories] and
                            'pharmacity.vn' in full_url and
                            not any(skip in full_url for skip in ['search', 'cart', 'account', 'login'])):

                            subcategories.append((text, full_url))

            # Cách 2: Tìm tất cả links có pattern thuốc
            all_sub_links = sub_soup.find_all('a', href=True)
            for link in all_sub_links:
                href = link.get('href')
                text = link.get_text(strip=True)

                if href and text and len(text) > 3:
                    if href.startswith('/'):
                        full_url = 'https://www.pharmacity.vn' + href
                    elif href.startswith('https://www.pharmacity.vn'):
                        full_url = href
                    else:
                        continue

                    # Loại bỏ query parameters
                    if '?' in full_url:
                        full_url = full_url.split('?')[0]

                    # Kiểm tra pattern subcategory mẹ và bé cụ thể hơn
                    subcategory_patterns = [
                        'sua-bot-tre-em', 'sua-cong-thuc', 'ta-bim-em-be', 'ta-quan', 'cham-soc-da-be',
                        'cham-soc-toc-be', 'vitamin-cho-be', 'thuc-pham-bo-sung-cho-be', 'do-choi-giao-duc',
                        'do-choi-van-dong', 'cham-soc-sau-sinh', 'vitamin-cho-me', 'thuc-pham-cho-me-bau',
                        'sua-me-bau', 'cham-soc-rang-mieng-be', 'bot-an-dam-cho-be'
                    ]

                    if (any(pattern in full_url for pattern in subcategory_patterns) and
                        full_url != main_url and
                        full_url not in [sub[1] for sub in subcategories] and
                        'pharmacity.vn' in full_url):

                        subcategories.append((text, full_url))

            print(f"  📊 Tìm thấy {len(subcategories)} subcategories:")
            for i, (sub_name, sub_url) in enumerate(subcategories[:10]):  # Hiển thị 10 đầu
                print(f"    {i+1}. {sub_name}")

            if len(subcategories) > 10:
                print(f"    ... và {len(subcategories) - 10} subcategories khác")

            # Thêm category cha vào danh sách
            all_categories.append((main_name, main_url))

            # Thêm tất cả subcategories
            all_categories.extend(subcategories)

        except Exception as e:
            print(f"  ❌ Lỗi khi phân tích {main_name}: {e}")
            # Vẫn thêm category cha nếu không lấy được subcategories
            all_categories.append((main_name, main_url))

    print(f"\n✅ HOÀN THÀNH KHÁM PHÁ CATEGORIES")
    print(f"📊 Tổng cộng: {len(all_categories)} categories")
    print("="*60)

    return [url for name, url in all_categories]

def crawl_single_product(driver, product_url, sheet, row, dynamic_columns):
    """Crawl thông tin chi tiết của một sản phẩm"""
    try:
        print(f"🔍 Đang crawl: {product_url}")
        # Không cần driver.get() vì đã mở tab mới
        time.sleep(2)
        product_soup = BeautifulSoup(driver.page_source, "html.parser")

        # Khởi tạo dữ liệu mặc định cho 16 cột
        product_name = "Không có"
        product_code = "Không có"
        brand = "Không có"
        price = "Không có"
        category = "Không có"
        manufacturer = "Không có"
        registration_number = "Không có"
        product_classification = "Không có"  # Thay thế cho prescription_required
        active_ingredient = "Không có"
        indication = "Không có"
        target_user = "Không có"
        dosage_form = "Không có"
        specification = "Không có"
        usage = "Không có"
        product_images = "Không có"

        # Khởi tạo cột từ pmc-content-html
        tong_hop = "Không có"  # Chỉ dùng cho TH1
        pmc_columns = {}  # Dictionary để lưu các cột động từ TH2

        # Lấy tên sản phẩm
        name_selectors = [
            'h1[data-testid="product-name"]',
            'h1.product-name',
            'h1',
            '.product-title h1',
            '[data-testid="product-title"]'
        ]
        for selector in name_selectors:
            name_element = product_soup.select_one(selector)
            if name_element:
                product_name = name_element.text.strip()
                break

        # Lấy mã sản phẩm từ cấu trúc HTML cụ thể
        import re

        # Phương pháp 1: Tìm span chứa mã sản phẩm bắt đầu bằng "P" (ưu tiên)
        all_spans = product_soup.find_all('span')
        for span in all_spans:
            span_text = span.get_text(strip=True)
            # Tìm mã sản phẩm bắt đầu bằng P và có 5-6 chữ số
            if re.match(r'^P\d{5,6}$', span_text):
                product_code = span_text
                break

        # Phương pháp 2: Nếu không tìm thấy, tìm trong text gần thương hiệu
        if product_code == "Không có":
            # Tìm thẻ a chứa thương hiệu
            brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
            if brand_link:
                # Tìm trong parent element của thương hiệu
                parent = brand_link.parent
                if parent:
                    parent_text = parent.get_text()
                    code_match = re.search(r'P\d{5,6}', parent_text)
                    if code_match:
                        product_code = code_match.group()

        # Phương pháp 3: Tìm trong toàn bộ text trang (fallback)
        if product_code == "Không có":
            page_text = product_soup.get_text()
            code_match = re.search(r'P\d{5,6}', page_text)
            if code_match:
                product_code = code_match.group()

        # Lấy thương hiệu từ link "Thương hiệu:"
        brand_link = product_soup.find('a', href=lambda x: x and '/thuong-hieu/' in x)
        if brand_link:
            brand = brand_link.text.strip().replace("Thương hiệu: ", "")

        # Lấy giá và đơn vị từ text trên trang
        page_text = product_soup.get_text()
        import re
        unit = "Không có"

        # Tìm giá có đơn vị cụ thể (ví dụ: "86.000 ₫/Kit")
        price_with_unit_patterns = [
            r'(\d+[.,]?\d*)\s*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)\xa0₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)',
            r'(\d+[.,]?\d*)[\s\xa0]*₫/(Kit|Hộp|Chai|Lọ|Vỉ|Gói|Bộ|Tuýp)'
        ]

        found_price_unit = False
        for pattern in price_with_unit_patterns:
            price_with_unit_match = re.search(pattern, page_text)
            if price_with_unit_match:
                price = price_with_unit_match.group(1) + " ₫"
                unit = price_with_unit_match.group(2)
                found_price_unit = True
                break

        if not found_price_unit:
            # Tìm giá thông thường
            price_match = re.search(r'(\d+\.?\d*)\s*₫', page_text)
            if price_match:
                price = price_match.group(0)
                unit = "Không có"

        # Lấy số đăng ký từ pattern cụ thể
        reg_patterns = [
            r'Số đăng ký:\s*(\d+)',
            r'(\d{12})',  # Pattern cho số đăng ký 12 chữ số
        ]
        for pattern in reg_patterns:
            reg_match = re.search(pattern, page_text)
            if reg_match:
                registration_number = reg_match.group(1) if reg_match.group(1).isdigit() else reg_match.group(0)
                break

        # Lấy danh mục từ cấu trúc p + div
        # Tìm thẻ p có text "Danh mục" và lấy div ngay dưới nó
        danh_muc_elements = product_soup.find_all('p', string=lambda text: text and 'Danh mục' in text)

        for danh_muc_p in danh_muc_elements:
            # Tìm div ngay sau thẻ p này
            next_div = danh_muc_p.find_next_sibling('div')
            if next_div:
                category_text = next_div.get_text(strip=True)
                if category_text and len(category_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    category_text = re.sub(r'\s+', ' ', category_text)  # Thay nhiều khoảng trắng bằng 1
                    category = category_text
                    break

        # Nếu không tìm thấy bằng cách trên, thử tìm theo cấu trúc khác
        if category == "Không có":
            # Tìm theo cấu trúc grid với text "Danh mục"
            grid_elements = product_soup.find_all('div', class_=lambda x: x and 'grid' in x)
            for grid in grid_elements:
                if 'Danh mục' in grid.get_text():
                    # Tìm div chứa nội dung danh mục trong grid này
                    content_divs = grid.find_all('div')
                    for div in content_divs:
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 5 and 'Danh mục' not in div_text:
                            # Kiểm tra nếu có vẻ như là danh mục (không phải số, không phải link)
                            if not div_text.isdigit() and 'http' not in div_text.lower():
                                category_text = re.sub(r'\s+', ' ', div_text)
                                category = category_text
                                break
                    if category != "Không có":
                        break

        # Fallback: Tìm từ patterns trong text (giữ lại logic cũ)
        if category == "Không có":
            page_text = product_soup.get_text()
            if "Dầu, Cao xoa bóp" in page_text:
                category = "Dầu, Cao xoa bóp"
            elif "Thuốc không kê đơn" in page_text:
                category = "Thuốc không kê đơn"
            else:
                # Tìm từ patterns
                category_patterns = [
                    r'Danh mục([^A-Z]*?)Nhà sản xuất',
                    r'Danh mục([^A-Z]*?)(?=Nhà|Hoạt|Chỉ|Dạng|Quy)',
                    r'Danh mục(.*?)(?=Nhà sản xuất|Hoạt chất)'
                ]
                for pattern in category_patterns:
                    match = re.search(pattern, page_text)
                    if match:
                        cat_text = match.group(1).strip()
                        # Lấy phần cuối cùng sau dấu "..."
                        if "..." in cat_text:
                            category = cat_text.split("...")[-1].strip()
                        else:
                            category = cat_text
                        break

        # Tìm nhà sản xuất
        manufacturer_patterns = [
            r'Nhà sản xuất([^A-Z]*?)Hoạt chất',
            r'Nhà sản xuất([^A-Z]*?)(?=Hoạt|Chỉ|Dạng|Quy)',
            r'Nhà sản xuất(.*?)(?=Hoạt chất|Chỉ định)'
        ]
        for pattern in manufacturer_patterns:
            match = re.search(pattern, page_text)
            if match:
                manufacturer = match.group(1).strip()
                break

        # Tìm hoạt chất
        active_patterns = [
            r'Hoạt chất([^A-Z]*?)Chỉ định',
            r'Hoạt chất([^A-Z]*?)(?=Chỉ|Dạng|Quy)',
            r'Hoạt chất(.*?)(?=Chỉ định|Dạng bào chế)'
        ]
        for pattern in active_patterns:
            match = re.search(pattern, page_text)
            if match:
                active_ingredient = match.group(1).strip()
                break

        # Tìm chỉ định
        indication_patterns = [
            r'Chỉ định([^A-Z]*?)Dạng bào chế',
            r'Chỉ định([^A-Z]*?)(?=Dạng|Quy)',
            r'Chỉ định(.*?)(?=Dạng bào chế|Quy cách)'
        ]
        for pattern in indication_patterns:
            match = re.search(pattern, page_text)
            if match:
                indication = match.group(1).strip()
                break

        # Tìm dạng bào chế
        dosage_patterns = [
            r'Dạng bào chế([^A-Z]*?)Quy cách',
            r'Dạng bào chế([^A-Z]*?)(?=Quy)',
            r'Dạng bào chế(.*?)(?=Quy cách|Lưu ý)'
        ]
        for pattern in dosage_patterns:
            match = re.search(pattern, page_text)
            if match:
                dosage_form = match.group(1).strip()
                break

        # Lấy quy cách từ cấu trúc p + div
        # Tìm thẻ p có text "Quy cách" và lấy div ngay dưới nó
        quy_cach_elements = product_soup.find_all('p', string=lambda text: text and 'Quy cách' in text)

        for quy_cach_p in quy_cach_elements:
            # Tìm div ngay sau thẻ p này
            next_div = quy_cach_p.find_next_sibling('div')
            if next_div:
                spec_text = next_div.get_text(strip=True)
                if spec_text and len(spec_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    spec_text = re.sub(r'\s+', ' ', spec_text)  # Thay nhiều khoảng trắng bằng 1
                    specification = spec_text
                    break

        # Nếu không tìm thấy bằng cách trên, thử tìm theo cấu trúc khác
        if specification == "Không có":
            # Tìm theo cấu trúc grid với text "Quy cách"
            grid_elements = product_soup.find_all('div', class_=lambda x: x and 'grid' in x)
            for grid in grid_elements:
                if 'Quy cách' in grid.get_text():
                    # Tìm div chứa nội dung quy cách trong grid này
                    content_divs = grid.find_all('div')
                    for div in content_divs:
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 5 and 'Quy cách' not in div_text:
                            # Kiểm tra nếu có vẻ như là quy cách (không phải số đơn thuần, không phải link)
                            if not div_text.isdigit() and 'http' not in div_text.lower():
                                spec_text = re.sub(r'\s+', ' ', div_text)
                                specification = spec_text
                                break
                    if specification != "Không có":
                        break

        # Fallback: Tìm từ patterns trong text (giữ lại logic cũ)
        if specification == "Không có":
            page_text = product_soup.get_text()
            spec_patterns = [
                r'Quy cách([^A-Z]*?)Lưu ý',
                r'Quy cách([^A-Z]*?)(?=Lưu|Đủ)',
                r'Quy cách(.*?)(?=Lưu ý|Đủ thuốc)'
            ]
            for pattern in spec_patterns:
                match = re.search(pattern, page_text)
                if match:
                    specification = match.group(1).strip()
                    break

        # Lấy phân loại sản phẩm từ cấu trúc label + div + button + span
        product_classifications = []
        classification_keywords = ['Kit', 'Hộp', 'Chai', 'Lọ', 'Vỉ', 'Gói', 'Bộ', 'Tuýp', 'Viên', 'Túi']

        # Tìm thẻ label có text "Phân loại sản phẩm"
        classification_labels = product_soup.find_all('label', string=lambda text: text and 'Phân loại sản phẩm' in text)

        for label in classification_labels:
            # Tìm div ngay sau label này
            next_div = label.find_next_sibling('div')
            if next_div:
                # Tìm tất cả button trong div này
                buttons = next_div.find_all('button')

                for button in buttons:
                    # Tìm span trong button
                    spans = button.find_all('span')

                    for span in spans:
                        span_text = span.get_text(strip=True)

                        # Lưu text span nếu không trống và không phải là số
                        if span_text and not span_text.isdigit() and span_text not in product_classifications:
                            # Kiểm tra nếu là từ khóa phân loại hợp lệ
                            if any(keyword.lower() in span_text.lower() for keyword in classification_keywords):
                                product_classifications.append(span_text)

        # Nếu không tìm thấy bằng cách trên, thử tìm theo cấu trúc khác
        if not product_classifications:
            # Tìm tất cả div có chứa text "Phân loại sản phẩm"
            all_divs = product_soup.find_all('div')
            for div in all_divs:
                if 'Phân loại sản phẩm' in div.get_text():
                    # Tìm button trong div này
                    buttons = div.find_all('button')

                    for button in buttons:
                        spans = button.find_all('span')
                        for span in spans:
                            span_text = span.get_text(strip=True)
                            if span_text and not span_text.isdigit() and span_text not in product_classifications:
                                # Kiểm tra nếu là từ khóa phân loại hợp lệ
                                if any(keyword.lower() in span_text.lower() for keyword in classification_keywords):
                                    product_classifications.append(span_text)

        # Nếu vẫn không tìm thấy, thử tìm trong text tổng thể
        if not product_classifications:
            for keyword in classification_keywords:
                if keyword in page_text:
                    product_classifications.append(keyword)
                    break

        # Gộp tất cả phân loại thành chuỗi
        if product_classifications:
            product_classification = ", ".join(product_classifications)
        else:
            product_classification = "Không có"

        # Đối tượng sử dụng - tìm từ chống chỉ định
        if "Trẻ nhỏ dưới" in page_text:
            target_user = "Người lớn và trẻ em trên 5 tuổi"
        elif "Phụ nữ có thai" in page_text and "chống chỉ định" in page_text.lower():
            target_user = "Không dành cho phụ nữ có thai"
        else:
            target_user = "Người lớn"

        # Tìm cách dùng
        usage_patterns = [
            r'Bôi.*?ngày\s*\d+\s*-\s*\d+\s*lần',
            r'\d+\s*viên.*?ngày',
            r'Liều dùng.*?ngày.*?lần',
            r'Cách dùng.*?ngày.*?lần'
        ]

        for pattern in usage_patterns:
            usage_match = re.search(pattern, page_text, re.IGNORECASE)
            if usage_match:
                usage = usage_match.group(0)
                break

        # Tìm lưu ý - tìm thẻ p có text "Lưu ý" và lấy div ngay dưới nó
        notes = "Không có"

        # Tìm thẻ p chứa text "Lưu ý"
        luu_y_elements = product_soup.find_all('p', string=lambda text: text and 'Lưu ý' in text)

        for luu_y_p in luu_y_elements:
            # Tìm div ngay sau thẻ p này
            next_div = luu_y_p.find_next_sibling('div')
            if next_div:
                notes_text = next_div.get_text(strip=True)
                if notes_text and len(notes_text) > 3:  # Đảm bảo có nội dung thực sự
                    # Làm sạch text
                    notes_text = re.sub(r'\s+', ' ', notes_text)  # Thay nhiều khoảng trắng bằng 1
                    notes = notes_text
                    break

        # Nếu không tìm thấy bằng cách trên, thử tìm theo cấu trúc khác
        if notes == "Không có":
            # Tìm theo cấu trúc grid với text "Lưu ý"
            grid_elements = product_soup.find_all('div', class_=lambda x: x and 'grid' in x)
            for grid in grid_elements:
                if 'Lưu ý' in grid.get_text():
                    # Tìm div chứa nội dung lưu ý trong grid này
                    content_divs = grid.find_all('div')
                    for div in content_divs:
                        div_text = div.get_text(strip=True)
                        if div_text and len(div_text) > 5 and 'Lưu ý' not in div_text:
                            # Kiểm tra nếu có vẻ như là lưu ý (không phải số đơn thuần, không phải link)
                            if not div_text.isdigit() and 'http' not in div_text.lower():
                                notes_text = re.sub(r'\s+', ' ', div_text)
                                notes = notes_text
                                break
                    if notes != "Không có":
                        break

        # Lấy ảnh sản phẩm
        img_elements = product_soup.find_all('img')
        image_urls = []
        for img in img_elements:
            src = img.get('src')
            if src and ('product' in src.lower() or 'pharmacity' in src.lower()):
                if src.startswith('//'):
                    src = 'https:' + src
                elif src.startswith('/'):
                    src = 'https://www.pharmacity.vn' + src
                if src not in image_urls:
                    image_urls.append(src)

        if image_urls:
            product_images = ", ".join(image_urls[:3])  # Lấy tối đa 3 ảnh

        # Xử lý pmc-content-html (TH1 và TH2)
        pmc_content_divs = product_soup.find_all('div', class_='pmc-content-html')

        if len(pmc_content_divs) == 1:
            # TH1: Chỉ có 1 div pmc-content-html -> ghi vào cột "Tổng hợp"
            tong_hop_content = pmc_content_divs[0].get_text(strip=True)
            if tong_hop_content:
                tong_hop = tong_hop_content
        elif len(pmc_content_divs) >= 2:
            # TH2: Có 2+ div pmc-content-html -> tạo cột động với ID thẻ cha
            for i, div in enumerate(pmc_content_divs):
                content = div.get_text(strip=True)
                if content:
                    # Tìm ID của thẻ cha
                    column_name = None
                    parent_element = div.parent

                    # Tìm ID từ thẻ cha hoặc các thẻ cha phía trên
                    current_element = parent_element
                    while current_element and column_name is None:
                        if current_element.get('id'):
                            column_name = current_element.get('id')
                            break
                        current_element = current_element.parent

                    # Nếu không tìm thấy ID, dùng fallback
                    if not column_name:
                        column_name = f"PMC Content {i+1}"

                    pmc_columns[column_name] = content

        # Tạo dữ liệu cơ bản (19 cột cố định)
        basic_data = [
            product_url, product_name, product_code, brand, price, unit, category,
            manufacturer, registration_number, product_classification, active_ingredient,
            indication, target_user, dosage_form, specification, usage, notes, product_images, tong_hop
        ]

        # Ghi dữ liệu cơ bản vào 19 cột đầu với retry logic
        range_basic = f"A{row}:S{row}"
        for attempt in range(3):  # Thử tối đa 3 lần
            try:
                sheet.update(range_basic, [basic_data])
                break  # Thành công thì thoát khỏi loop
            except Exception as api_error:
                print(f"⚠️ Lỗi API lần {attempt + 1}: {api_error}")
                if attempt < 2:  # Chưa hết số lần thử
                    time.sleep(2 ** attempt)  # Exponential backoff: 1s, 2s, 4s
                else:
                    raise api_error  # Hết số lần thử thì raise lỗi
        time.sleep(1)  # Delay 1 giây sau khi ghi dữ liệu cơ bản

        # Xử lý các cột động từ pmc_columns (TH2)
        if pmc_columns:
            for column_name, content in pmc_columns.items():
                # Kiểm tra xem cột này đã tồn tại chưa
                if column_name not in dynamic_columns:
                    # Tạo cột mới
                    next_col_index = len(dynamic_columns) + 20  # Bắt đầu từ cột T (20)
                    dynamic_columns[column_name] = next_col_index

                    # Ghi header cho cột mới với retry logic
                    col_letter = get_column_letter(next_col_index)
                    for attempt in range(3):
                        try:
                            sheet.update(f"{col_letter}1", [[column_name]])
                            break
                        except Exception as api_error:
                            print(f"⚠️ Lỗi API header lần {attempt + 1}: {api_error}")
                            if attempt < 2:
                                time.sleep(2 ** attempt)
                            else:
                                raise api_error
                    time.sleep(0.5)  # Delay 0.5 giây sau khi tạo header

                # Ghi nội dung vào cột với retry logic
                col_index = dynamic_columns[column_name]
                col_letter = get_column_letter(col_index)
                for attempt in range(3):
                    try:
                        sheet.update(f"{col_letter}{row}", [[content]])
                        break
                    except Exception as api_error:
                        print(f"⚠️ Lỗi API content lần {attempt + 1}: {api_error}")
                        if attempt < 2:
                            time.sleep(2 ** attempt)
                        else:
                            raise api_error
                time.sleep(0.5)  # Delay 0.5 giây sau mỗi lần ghi cột động

        print(f"✅ Đã ghi sản phẩm: {product_name}")

    except Exception as e:
        print(f"❌ Lỗi khi crawl sản phẩm: {e}")

def load_more_products(driver, max_products=None):
    """Click nút Xem thêm liên tục cho đến khi nút biến mất hoặc đủ số lượng sản phẩm"""
    print(f"\n🔄 Click 'Xem thêm' liên tục cho đến khi hết{f' hoặc đủ {max_products} sản phẩm' if max_products else ''}...")

    click_count = 0

    while True:
        try:
            # Đếm số product-card hiện tại
            product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
            current_count = len(product_cards)
            print(f"  Lần {click_count + 1}: {current_count} product-card")

            # Kiểm tra nếu đã đủ số lượng sản phẩm cần thiết
            if max_products and current_count >= max_products:
                print(f"    ✅ Đã đủ {max_products} sản phẩm, dừng click 'Xem thêm'")
                break

            # Scroll xuống cuối trang
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

            # Tìm nút "Xem thêm" với selector chính xác
            try:
                # Thử tìm nút bằng XPath
                load_more_btn = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//button//span[text()='Xem thêm']"))
                )

                if load_more_btn and load_more_btn.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", load_more_btn)
                    time.sleep(1)

                    # Click nút
                    try:
                        load_more_btn.click()
                        print(f"    ✅ Đã click 'Xem thêm'")
                        click_count += 1
                        time.sleep(4)  # Chờ load sản phẩm mới
                    except:
                        # Thử click bằng JavaScript
                        driver.execute_script("arguments[0].click();", load_more_btn)
                        print(f"    ✅ Đã click 'Xem thêm' bằng JS")
                        click_count += 1
                        time.sleep(4)
                else:
                    print(f"    🛑 Nút 'Xem thêm' không hiển thị - Đã hết sản phẩm")
                    break

            except TimeoutException:
                print(f"    🛑 Không tìm thấy nút 'Xem thêm' - Đã hết sản phẩm")
                break
            except Exception as e:
                print(f"    🛑 Lỗi khi tìm nút 'Xem thêm': {e}")
                break

        except Exception as e:
            print(f"    ❌ Lỗi: {e}")
            break

    # Đếm số product-card cuối cùng
    final_product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
    final_count = len(final_product_cards)

    print(f"✅ Hoàn thành sau {click_count} lần click")
    print(f"📊 Tổng số product-card: {final_count}")

    return final_product_cards
# ===== CẤU HÌNH SỐ LƯỢNG SẢN PHẨM =====
MAX_PRODUCTS = 10000
  # Thay đổi số này để điều chỉnh số lượng sản phẩm muốn crawl
# Ví dụ: MAX_PRODUCTS = 100 (chỉ lấy 100 sản phẩm)
#        MAX_PRODUCTS = None (lấy tất cả sản phẩm)

def main():
    """Hàm main với error handling"""
    global driver

    try:
        # Khởi động trình duyệt với ChromeDriver tự động cập nhật - FULL SCREEN
        print("🚀 Đang khởi tạo Chrome browser FULL SCREEN...")
        service = Service(ChromeDriverManager().install())

        # Cấu hình Chrome options để tránh lỗi
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")

        # QUAN TRỌNG: Thiết lập full screen để hiển thị nhiều sản phẩm hơn
        chrome_options.add_argument("--start-maximized")  # Mở browser ở chế độ maximize
        chrome_options.add_argument("--window-size=1920,1080")  # Đặt kích thước cụ thể
        chrome_options.add_argument("--force-device-scale-factor=1")  # Đảm bảo scale 100%

        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

        # Tắt logging để giảm lỗi console
        chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_argument("--log-level=3")  # Chỉ hiển thị lỗi nghiêm trọng

        # Sử dụng global driver để có thể cleanup
        driver = webdriver.Chrome(service=service, options=chrome_options)

        # Đảm bảo browser thực sự full screen
        driver.maximize_window()

        # Kiểm tra kích thước màn hình
        window_size = driver.get_window_size()
        print(f"📐 Kích thước browser: {window_size['width']}x{window_size['height']}")
        print("✅ Chrome browser FULL SCREEN đã sẵn sàng!")

        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Đảm bảo sheet có ít nhất 50 cột để tránh lỗi
        ensure_sheet_columns(sheet, 50)

        # Tạo header cố định trước (19 cột: 18 cột cơ bản + "Tổng hợp")
        fixed_header = ["URL", "Tên sản phẩm", "Mã sản phẩm", "Thương hiệu", "Giá", "Đơn vị", "Danh mục", "Nhà sản xuất", "Số đăng ký", "Phân loại sản phẩm", "Hoạt chất", "Chỉ định", "Đối tượng sử dụng", "Dạng bào chế", "Quy cách", "Cách dùng", "Lưu ý", "Ảnh sản phẩm", "Tổng hợp"]
        sheet.update("A1:S1", [fixed_header])
        print(f"✅ Đã tạo header cố định với {len(fixed_header)} cột")

        row = 2  # Bắt đầu ghi data từ hàng 2
        dynamic_columns = {}  # Dictionary để track các cột động đã tạo {column_name: column_index}

        print(f"Cấu hình: Sẽ crawl tối đa {MAX_PRODUCTS if MAX_PRODUCTS else 'TẤT CẢ'} sản phẩm")

        # Tự động khám phá tất cả categories từ trang Mẹ và bé
        print("🚀 BẮT ĐẦU TỰ ĐỘNG KHÁM PHÁ CATEGORIES MẸ VÀ BÉ...")
        pharmacity_categories = discover_all_categories(driver)

        all_product_urls = []

        print(f"\n📋 Sẽ crawl từ {len(pharmacity_categories)} categories đã khám phá được")

        # Crawl từ tất cả các danh mục
        for category_url in pharmacity_categories:
            # Kiểm tra nếu đã đủ số lượng sản phẩm
            if MAX_PRODUCTS and len(all_product_urls) >= MAX_PRODUCTS:
                print(f"Đã đủ {MAX_PRODUCTS} sản phẩm, dừng thu thập URL")
                break

            print(f"\n🏷️ Đang crawl danh mục: {category_url}")
            driver.get(category_url)
            time.sleep(5)

            # Load thêm sản phẩm bằng cách click "Xem thêm" liên tục (chỉ load đủ số lượng cần thiết)
            products = load_more_products(driver, MAX_PRODUCTS)

            # Lấy tất cả product-card sau khi đã load hết
            product_cards = driver.find_elements(By.CLASS_NAME, "product-card")
            print(f"📊 Tìm thấy {len(product_cards)} product-card")

            # Crawl từng product-card ngay khi tìm thấy (không thu thập hết rồi mới crawl)
            category_crawled = 0
            for i, card in enumerate(product_cards):
                # Kiểm tra nếu đã đủ số lượng sản phẩm tổng
                if MAX_PRODUCTS and row - 2 >= MAX_PRODUCTS:
                    print(f"Đã đủ {MAX_PRODUCTS} sản phẩm, dừng crawl")
                    break

                try:
                    # Scroll đến product-card
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", card)
                    time.sleep(0.5)

                    # Tìm link trong product-card
                    try:
                        product_link = card.find_element(By.TAG_NAME, "a")
                        product_url = product_link.get_attribute("href")

                        if product_url:
                            # Loại bỏ query parameters
                            if "?" in product_url:
                                product_url = product_url.split("?")[0]

                            print(f"\n� Crawl sản phẩm {i+1}/{len(product_cards)} trong category")
                            print(f"📄 URL: {product_url}")

                            # Thêm URL vào danh sách tổng
                            all_product_urls.append(product_url)

                            # Mở sản phẩm trong tab mới và crawl ngay
                            driver.execute_script("window.open(arguments[0], '_blank');", product_url)
                            driver.switch_to.window(driver.window_handles[1])
                            time.sleep(3)

                            # Crawl thông tin sản phẩm
                            crawl_single_product(driver, product_url, sheet, row, dynamic_columns)

                            # Đóng tab và quay về tab chính
                            driver.close()
                            driver.switch_to.window(driver.window_handles[0])
                            time.sleep(1)

                            category_crawled += 1
                            row += 1

                        else:
                            print(f"⚠️ Product-card {i+1} không có link")

                    except Exception as e:
                        print(f"❌ Lỗi khi lấy URL product-card {i+1}: {e}")
                        continue

                except Exception as e:
                    print(f"❌ Lỗi tổng quát product-card {i+1}: {e}")
                    # Đảm bảo quay về tab chính nếu có lỗi
                    if len(driver.window_handles) > 1:
                        driver.close()
                        driver.switch_to.window(driver.window_handles[0])
                    continue

            print(f"✅ Hoàn thành category: {category_crawled} sản phẩm đã crawl")




        print("\n" + "="*60)
        print("🎉 HOÀN TẤT CRAWL PHARMACITY MẸ VÀ BÉ!")
        print("="*60)
        print(f"📊 Tổng số URLs thu thập: {len(all_product_urls)}")
        print(f"📝 Tổng số sản phẩm đã crawl: {row - 2}")
        print(f"🔗 Link Google Sheets MomAndBaby: https://docs.google.com/spreadsheets/d/1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8")
        print("="*60)

    except KeyboardInterrupt:
        print("\n⚠️ Script bị dừng bởi người dùng (Ctrl+C)")
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup sẽ được gọi tự động bởi atexit
        print("🧹 Cleanup sẽ được thực hiện tự động...")

if __name__ == "__main__":
    main()