import pymysql
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import time
import sys
import os
import unicodedata
import re

# ===== CẤU HÌNH =====
# Google Sheets config
SHEET_ID = "1oloh7S0Z2KfCmkNjLC3qd4oPf7xJNT8exF3y308JJc8"
SHEET_NAME = "Medicine"  # Sheet Medicine như file insert_adminer
CREDENTIALS_FILE = "medical-crawl-2024-013b6faaa588.json"

# MySQL config cho Docker Adminer
DB_CONFIG = {
    'host': 'localhost',     # Hoặc thử 'mysql' nếu localhost không được
    'port': 3307,            # Port Docker MySQL (thường là 3307)
    'user': 'sail',          # Username từ Adminer
    'password': 'password',  # Password từ Adminer
    'database': 'bagisto',   # Database từ Adminer
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# ===== CẤU HÌNH TEST =====
ROW_NUMBER = None  # 🎯 KIỂM SOÁT SỐ HÀNG: Số dòng tối đa muốn xử lý (None = tất cả dòng)
START_ROW = 2  # Bắt đầu từ dòng nào (2 = bỏ qua header)

# ===== CẤU HÌNH ACTIVE INGREDIENTS =====
ATTRIBUTE_ID = 44  # attribute_id mặc định cho attribute_options
SORT_ORDER = 1  # sort_order mặc định
COLUMN_INDEX = 10  # Cột K (index 10) - Hoạt chất

# ===== CẤU HÌNH CÁC BẢNG CẦN INSERT =====
TABLES_TO_INSERT = [
    "attribute_options",
    "active_ingredients"
]

# ===== THÔNG TIN MÔ TẢ HOẠT CHẤT =====
INGREDIENT_DESCRIPTIONS = {
    'paracetamol': 'Thuốc giảm đau, hạ sốt phổ biến, an toàn khi sử dụng đúng liều lượng.',
    'ibuprofen': 'Thuốc chống viêm không steroid, giảm đau, hạ sốt và chống viêm hiệu quả.',
    'aspirin': 'Thuốc giảm đau, hạ sốt, chống viêm và ngăn ngừa đông máu.',
    'amoxicillin': 'Kháng sinh nhóm penicillin, điều trị nhiễm khuẩn do vi khuẩn nhạy cảm.',
    'cetirizine': 'Thuốc kháng histamin thế hệ 2, điều trị dị ứng, mày đay, viêm mũi dị ứng.',
    'loratadine': 'Thuốc kháng histamin không gây buồn ngủ, điều trị viêm mũi dị ứng.',
    'omeprazole': 'Thuốc ức chế bơm proton, điều trị loét dạ dày, trào ngược dạ dày thực quản.',
    'metformin': 'Thuốc điều trị tiểu đường type 2, giảm đường huyết và tăng độ nhạy insulin.',
    'simvastatin': 'Thuốc statin giảm cholesterol, ngăn ngừa bệnh tim mạch.',
    'amlodipine': 'Thuốc chẹn kênh canxi, điều trị tăng huyết áp và đau thắt ngực.',
    'default': 'Hoạt chất dược phẩm có tác dụng điều trị bệnh theo chỉ định của bác sĩ.'
}

def connect_to_google_sheets():
    """Kết nối Google Sheets"""
    print("🔗 Đang kết nối Google Sheets...")
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_name(CREDENTIALS_FILE, scope)
        client = gspread.authorize(creds)
        sheet = client.open_by_key(SHEET_ID).worksheet(SHEET_NAME)
        print("✅ Đã kết nối Google Sheets thành công!")
        return sheet
    except Exception as e:
        print(f"❌ Lỗi kết nối Google Sheets: {e}")
        sys.exit(1)

def connect_to_mysql():
    """Kết nối MySQL Docker"""
    print("🔗 Đang kết nối MySQL Docker (Adminer)...")
    print(f"🖥️ Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"👤 User: {DB_CONFIG['user']}")
    print(f"🗄️ Database: {DB_CONFIG['database']}")

    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Đã kết nối MySQL Docker thành công!")
        return connection
    except Exception as e:
        print(f"❌ Lỗi kết nối MySQL Docker: {e}")
        print("💡 Thử các cách sau:")
        print("   - Đảm bảo Docker đang chạy")
        print("   - Kiểm tra port 3307 có đúng không")
        print("   - Thử thay 'localhost' bằng 'mysql' hoặc '127.0.0.1'")
        print("   - Kiểm tra Adminer: http://localhost:8080")
        sys.exit(1)

def check_tables_exist(connection):
    """Kiểm tra các bảng trong Docker MySQL"""
    for table_name in TABLES_TO_INSERT:
        print(f"🔧 Kiểm tra table {table_name} trong Docker MySQL...")

        # Kiểm tra xem table đã tồn tại chưa
        check_table_sql = f"""
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = '{DB_CONFIG['database']}'
        AND table_name = '{table_name}'
        """

        try:
            with connection.cursor() as cursor:
                cursor.execute(check_table_sql)
                result = cursor.fetchone()

                if result['count'] > 0:
                    print(f"✅ Table {table_name} đã tồn tại trong Docker!")

                    # Kiểm tra cấu trúc table
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
                    print(f"📋 Cấu trúc table {table_name}:")
                    for col in columns[:5]:  # Chỉ hiển thị 5 cột đầu
                        print(f"  - {col['Field']}: {col['Type']}")
                    if len(columns) > 5:
                        print(f"  ... và {len(columns) - 5} cột khác")
                    print()
                else:
                    print(f"❌ Table {table_name} chưa tồn tại trong Docker!")
                    print("💡 Vui lòng tạo table trong Adminer trước: http://localhost:8080")
                    sys.exit(1)

        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra table {table_name}: {e}")
            sys.exit(1)

def get_data_from_sheet(sheet):
    """Lấy dữ liệu từ Google Sheets"""
    print("📋 Đang lấy dữ liệu từ Google Sheets...")

    try:
        # Lấy tất cả dữ liệu từ sheet
        all_data = sheet.get_all_values()

        # Tính toán số dòng thực tế có data (bỏ qua header)
        total_rows_with_header = len(all_data)
        total_data_rows = total_rows_with_header - (START_ROW - 1)

        print(f"📊 Sheet có tổng cộng {total_rows_with_header} dòng (bao gồm header)")
        print(f"📊 Số dòng data thực tế: {total_data_rows} dòng")

        # Bỏ qua header
        data_rows = all_data[START_ROW-1:]

        # Giới hạn số lượng dòng nếu cần
        if ROW_NUMBER:
            if ROW_NUMBER > total_data_rows:
                print(f"⚠️  ROW_NUMBER ({ROW_NUMBER}) lớn hơn số dòng data thực tế ({total_data_rows})")
                print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data có sẵn")
            else:
                print(f"📋 Giới hạn xử lý {ROW_NUMBER} dòng đầu tiên từ {total_data_rows} dòng data")
                data_rows = data_rows[:ROW_NUMBER]
        else:
            print(f"📋 Sẽ xử lý TẤT CẢ {total_data_rows} dòng data")

        actual_rows_to_process = len(data_rows)
        print(f"✅ Đã lấy {actual_rows_to_process} dòng dữ liệu từ Google Sheets để xử lý")

        return data_rows
    except Exception as e:
        print(f"❌ Lỗi khi lấy dữ liệu từ Google Sheets: {e}")
        sys.exit(1)

def get_column_value(row, column_index):
    """Lấy giá trị từ cột theo index"""
    if len(row) > column_index:
        return row[column_index].strip() if row[column_index] else ""
    return ""

def create_slug(text):
    """Tạo slug từ text tiếng Việt"""
    # Chuyển về lowercase
    text = text.lower()

    # Loại bỏ dấu tiếng Việt
    text = unicodedata.normalize('NFD', text)
    text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')

    # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
    text = re.sub(r'[^a-z0-9]+', '-', text)

    # Loại bỏ dấu gạch ngang ở đầu và cuối
    text = text.strip('-')

    return text

def get_ingredient_description(ingredient_name):
    """Lấy mô tả cho hoạt chất"""
    # Tìm kiếm theo tên chính xác (lowercase)
    key = ingredient_name.lower().strip()

    # Tìm trong dictionary
    if key in INGREDIENT_DESCRIPTIONS:
        return INGREDIENT_DESCRIPTIONS[key]

    # Tìm kiếm partial match
    for known_ingredient, description in INGREDIENT_DESCRIPTIONS.items():
        if known_ingredient in key or key in known_ingredient:
            return description

    # Trả về mô tả mặc định
    return INGREDIENT_DESCRIPTIONS['default']

def check_attribute_option_exists(connection, admin_name):
    """Kiểm tra hoạt chất đã tồn tại trong bảng attribute_options chưa với attribute_id=44"""
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT id, admin_name, sort_order
            FROM attribute_options
            WHERE attribute_id = %s AND admin_name = %s
            """
            cursor.execute(sql, (ATTRIBUTE_ID, admin_name))
            result = cursor.fetchone()

            if result:
                print(f"      ⚠️ Hoạt chất '{admin_name}' đã tồn tại trong bảng attribute_options")
                print(f"          📋 ID={result['id']}, attribute_id={ATTRIBUTE_ID}, sort_order={result['sort_order']}")
                return result['id']
            else:
                return None

    except Exception as e:
        print(f"  ❌ Lỗi khi check hoạt chất trong attribute_options '{admin_name}': {e}")
        return None

def check_active_ingredient_exists(connection, name):
    """Kiểm tra hoạt chất đã tồn tại trong bảng active_ingredients chưa"""
    try:
        with connection.cursor() as cursor:
            sql = "SELECT id, name, slug FROM active_ingredients WHERE name = %s"
            cursor.execute(sql, (name,))
            result = cursor.fetchone()

            if result:
                print(f"      ⚠️ Hoạt chất '{name}' đã tồn tại trong bảng active_ingredients")
                print(f"          📋 ID={result['id']}, slug='{result['slug']}'")
                return result['id']
            else:
                return None

    except Exception as e:
        print(f"  ❌ Lỗi khi check hoạt chất trong active_ingredients '{name}': {e}")
        return None

def insert_attribute_option(connection, admin_name):
    """Insert hoạt chất vào bảng attribute_options với attribute_id=44"""
    try:
        with connection.cursor() as cursor:
            # Insert vào attribute_options
            sql = """
            INSERT INTO attribute_options (attribute_id, admin_name, sort_order, swatch_value, is_featured, brand_image, brand_description)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            values = (ATTRIBUTE_ID, admin_name, SORT_ORDER, None, 0, None, None)
            cursor.execute(sql, values)

            # Lấy ID vừa insert
            option_id = cursor.lastrowid
            print(f"        ✅ Inserted attribute_options: ID={option_id}")
            print(f"            📋 attribute_id={ATTRIBUTE_ID}, admin_name='{admin_name}', sort_order={SORT_ORDER}")

            return option_id

    except Exception as e:
        print(f"    ❌ Lỗi khi insert attribute_option '{admin_name}': {e}")
        raise e

def insert_active_ingredient(connection, name, description, slug):
    """Insert hoạt chất vào bảng active_ingredients"""
    try:
        with connection.cursor() as cursor:
            # Insert vào active_ingredients
            sql = """
            INSERT INTO active_ingredients (name, description, status, slug, created_at, updated_at)
            VALUES (%s, %s, %s, %s, NOW(), NOW())
            """
            values = (name, description, 1, slug)
            cursor.execute(sql, values)

            # Lấy ID vừa insert
            ingredient_id = cursor.lastrowid
            print(f"        ✅ Inserted active_ingredients: ID={ingredient_id}")
            print(f"            📋 name='{name}', slug='{slug}', status=1")
            print(f"            📋 description: {description[:50]}...")

            return ingredient_id

    except Exception as e:
        print(f"    ❌ Lỗi khi insert active_ingredient '{name}': {e}")
        raise e

def process_active_ingredients_from_row(row):
    """Xử lý cột K (hoạt chất) và tách theo dấu chấm phẩy"""
    # Lấy dữ liệu từ cột K
    ingredients_string = get_column_value(row, COLUMN_INDEX)

    if not ingredients_string:
        return []

    # Bỏ qua nếu giá trị là "Không có"
    if ingredients_string.strip().lower() == "không có":
        return []

    # Tách theo dấu chấm phẩy và làm sạch
    ingredients = [ingredient.strip() for ingredient in ingredients_string.split(';') if ingredient.strip()]

    return ingredients

def insert_active_ingredients_workflow(connection, data_rows):
    """Insert workflow: Đọc cột K → Tách theo dấu chấm phẩy → Insert 2 bảng"""
    print("🚀 BẮT ĐẦU WORKFLOW INSERT ACTIVE INGREDIENTS (DOCKER)")
    print("📋 Workflow: Đọc cột K → Tách theo dấu chấm phẩy → Insert attribute_options + active_ingredients")

    total_success = 0
    total_failed = 0
    total_skipped = 0
    processed_ingredients = set()  # Để tránh duplicate ingredients

    try:
        with connection.cursor() as cursor:
            for row_index, row in enumerate(data_rows, start=1):
                try:
                    print(f"\n🔄 Xử lý dòng {row_index}:")

                    # Lấy dữ liệu từ cột K
                    if len(row) <= COLUMN_INDEX:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Không có dữ liệu cột K")
                        total_skipped += 1
                        continue

                    ingredients = process_active_ingredients_from_row(row)

                    if not ingredients:
                        print(f"  ⏭️ Bỏ qua dòng {row_index}: Cột K rỗng hoặc 'Không có'")
                        total_skipped += 1
                        continue

                    print(f"  📝 Dữ liệu cột K: {ingredients}")

                    # Xử lý từng hoạt chất
                    row_inserted = 0
                    for ingredient in ingredients:
                        print(f"    🔍 Kiểm tra hoạt chất '{ingredient}'...")

                        # Kiểm tra duplicate trong session
                        if ingredient in processed_ingredients:
                            print(f"      ⏭️ Hoạt chất '{ingredient}' đã xử lý trong session này")
                            continue

                        # Kiểm tra duplicate trong cả 2 bảng
                        option_exists = check_attribute_option_exists(connection, ingredient)
                        ingredient_exists = check_active_ingredient_exists(connection, ingredient)

                        if option_exists and ingredient_exists:
                            print(f"      ⏭️ Bỏ qua: Hoạt chất '{ingredient}' đã tồn tại trong cả 2 bảng")
                            processed_ingredients.add(ingredient)
                            continue

                        # Tạo slug và description
                        slug = create_slug(ingredient)
                        description = get_ingredient_description(ingredient)

                        print(f"      🆕 Tạo mới hoạt chất '{ingredient}' (slug: '{slug}')")

                        # Insert vào attribute_options nếu chưa có
                        if not option_exists:
                            insert_attribute_option(connection, ingredient)

                        # Insert vào active_ingredients nếu chưa có
                        if not ingredient_exists:
                            insert_active_ingredient(connection, ingredient, description, slug)

                        processed_ingredients.add(ingredient)
                        row_inserted += 1

                    if row_inserted > 0:
                        # Commit transaction cho từng dòng
                        connection.commit()
                        total_success += 1
                        print(f"  ✅ Đã xử lý {row_inserted} hoạt chất từ dòng {row_index}")
                    else:
                        total_skipped += 1

                except Exception as e:
                    print(f"  ❌ Lỗi khi xử lý dòng {row_index}: {e}")
                    total_failed += 1
                    connection.rollback()
                    continue

        print(f"\n📊 KẾT QUẢ WORKFLOW ACTIVE INGREDIENTS (DOCKER):")
        print(f"  ✅ Thành công: {total_success} dòng")
        print(f"  ⏭️ Bỏ qua: {total_skipped} dòng")
        print(f"  ❌ Thất bại: {total_failed} dòng")
        print(f"  📋 Tổng hoạt chất unique đã xử lý: {len(processed_ingredients)}")

    except Exception as e:
        print(f"❌ Lỗi workflow: {e}")
        connection.rollback()

def check_existing_data(connection):
    """Kiểm tra dữ liệu hiện có trong các tables"""
    for table_name in TABLES_TO_INSERT:
        print(f"🔍 Kiểm tra dữ liệu hiện có trong table {table_name} (Docker)...")

        try:
            with connection.cursor() as cursor:
                if table_name == "attribute_options":
                    # Tổng số records
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    result = cursor.fetchone()
                    total_count = result['count']

                    # Số records với attribute_id = 44
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name} WHERE attribute_id = %s", (ATTRIBUTE_ID,))
                    result = cursor.fetchone()
                    attribute_count = result['count']

                    print(f"📊 Hiện có {total_count} records trong table {table_name}")
                    print(f"📊 Hiện có {attribute_count} records với attribute_id={ATTRIBUTE_ID}")

                    # Hiển thị một vài mẫu
                    if attribute_count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} WHERE attribute_id = %s LIMIT 3", (ATTRIBUTE_ID,))
                        samples = cursor.fetchall()
                        print("📋 Mẫu dữ liệu:")
                        for sample in samples:
                            print(f"  - ID={sample['id']}, admin_name='{sample['admin_name']}', sort_order={sample['sort_order']}")
                else:
                    # active_ingredients table
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                    result = cursor.fetchone()
                    count = result['count']
                    print(f"📊 Hiện có {count} records trong table {table_name}")

                    # Hiển thị một vài mẫu
                    if count > 0:
                        cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                        samples = cursor.fetchall()
                        print("📋 Mẫu dữ liệu:")
                        for sample in samples:
                            print(f"  - ID={sample['id']}, name='{sample['name']}', slug='{sample['slug']}'")
                print()
        except Exception as e:
            print(f"❌ Lỗi khi kiểm tra dữ liệu {table_name}: {e}")

def main():
    """Hàm main cho Active Ingredients workflow"""
    print("🚀 BẮT ĐẦU INSERT ACTIVE INGREDIENTS VÀO DOCKER MYSQL (ADMINER)")
    print("="*60)
    print(f"📊 Cấu hình: Insert tối đa {ROW_NUMBER if ROW_NUMBER else 'TẤT CẢ'} dòng")
    print(f"🐳 Docker Database: {DB_CONFIG['database']}")
    print(f"🖥️ Docker Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"📋 Active Workflow: Active Ingredients từ cột K")
    print(f"📋 Tables: {', '.join(TABLES_TO_INSERT)}")
    print(f"📋 Attribute ID: {ATTRIBUTE_ID}")
    print(f"📋 Sort Order: {SORT_ORDER}")
    print(f"🌐 Adminer URL: http://localhost:8080")
    print("="*60)

    try:
        # Kết nối Google Sheets
        sheet = connect_to_google_sheets()

        # Kết nối Docker MySQL
        connection = connect_to_mysql()

        # Kiểm tra tables trong Docker
        check_tables_exist(connection)

        # Kiểm tra dữ liệu hiện có
        check_existing_data(connection)

        # Lấy dữ liệu từ Google Sheets
        data_rows = get_data_from_sheet(sheet)

        # Insert dữ liệu vào Docker MySQL
        insert_active_ingredients_workflow(connection, data_rows)

        # Kiểm tra lại sau khi insert
        print("\n🔍 Kiểm tra lại sau khi insert:")
        check_existing_data(connection)

        # Đóng kết nối
        connection.close()

        print("\n" + "="*60)
        print("🎉 HOÀN TẤT INSERT ACTIVE INGREDIENTS VÀO DOCKER!")
        print("🌐 Kiểm tra kết quả tại: http://localhost:8080")
        print("="*60)

    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()